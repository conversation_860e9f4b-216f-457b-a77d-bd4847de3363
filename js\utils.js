// Utility functions for the application

class Utils {
    // Grade calculation functions for different grade levels
    static calculateGrades(gradeLevel, scores) {
        let total = 0;
        let level = '';
        let description = '';

        if (gradeLevel >= 1 && gradeLevel <= 4) {
            // Grades 1-4: Total = 50
            total = (scores.oral1 || 0) + (scores.oral2 || 0) + 
                   (scores.practical1 || 0) + (scores.practical2 || 0) + 
                   (scores.project || 0);
            
            level = this.getLevel1to4(total);
            description = this.getDescription1to4(total);
        } else if (gradeLevel >= 5 && gradeLevel <= 10) {
            // Grades 5-10: Total = 100
            total = (scores.oral1 || 0) + (scores.oral2 || 0) + 
                   (scores.practical1 || 0) + (scores.practical2 || 0) + 
                   (scores.project || 0) + (scores.shortTest || 0);
            
            level = this.getLevel5to12(total);
            description = this.getDescription5to12(total);
        } else if (gradeLevel >= 11 && gradeLevel <= 12) {
            // Grades 11-12: Total = 100
            total = (scores.oral1 || 0) + (scores.oral2 || 0) + 
                   (scores.practical1 || 0) + (scores.practical2 || 0) + 
                   (scores.project || 0) + (scores.shortTest || 0) + 
                   (scores.finalExam || 0);
            
            level = this.getLevel5to12(total);
            description = this.getDescription5to12(total);
        }

        return {
            total: total,
            level: level,
            description: description
        };
    }

    // Get level for grades 1-4 (out of 50)
    static getLevel1to4(total) {
        if (total >= 47.25) return 'أ';
        if (total >= 44.75) return 'ب';
        if (total >= 39.75) return 'ج';
        if (total >= 34.75) return 'د';
        if (total >= 29.75) return 'هـ';
        if (total >= 24.75) return 'و';
        return 'ز';
    }

    // Get description for grades 1-4
    static getDescription1to4(total) {
        if (total === 0) return '';
        if (total >= 47.25) return '1';
        if (total >= 44.75) return '2';
        if (total >= 39.75) return '3';
        if (total >= 34.75) return '4';
        if (total >= 29.75) return '5';
        if (total >= 24.75) return '6';
        if (total < 24.75) return '7';
        return '';
    }

    // Get level for grades 5-12 (out of 100)
    static getLevel5to12(total) {
        if (total >= 90) return 'أ';
        if (total >= 80) return 'ب';
        if (total >= 70) return 'ج';
        if (total >= 60) return 'د';
        if (total >= 50) return 'هـ';
        return 'راسب';
    }

    // Get description for grades 5-12
    static getDescription5to12(total) {
        if (total >= 90) return 'ممتاز';
        if (total >= 80) return 'جيد جداً';
        if (total >= 70) return 'جيد';
        if (total >= 60) return 'مقبول';
        if (total >= 50) return 'ضعيف';
        return 'راسب';
    }

    // Get grade structure based on grade level
    static getGradeStructure(gradeLevel) {
        if (gradeLevel >= 1 && gradeLevel <= 4) {
            return {
                oral1: { max: 10, label: 'الأعمال الشفوية (الفترة الأولى)' },
                oral2: { max: 10, label: 'الأعمال الشفوية (الفترة الثانية)' },
                practical1: { max: 10, label: 'الأنشطة العملية (الفترة الأولى)' },
                practical2: { max: 10, label: 'الأنشطة العملية (الفترة الثانية)' },
                project: { max: 10, label: 'المشروع' },
                total: 50
            };
        } else if (gradeLevel >= 5 && gradeLevel <= 10) {
            return {
                oral1: { max: 10, label: 'الأعمال الشفوية (الفترة الأولى)' },
                oral2: { max: 10, label: 'الأعمال الشفوية (الفترة الثانية)' },
                practical1: { max: 20, label: 'الأنشطة العملية (الفترة الأولى)' },
                practical2: { max: 20, label: 'الأنشطة العملية (الفترة الثانية)' },
                project: { max: 20, label: 'المشروع' },
                shortTest: { max: 20, label: 'الاختبار القصير' },
                total: 100
            };
        } else if (gradeLevel >= 11 && gradeLevel <= 12) {
            return {
                oral1: { max: 5, label: 'الأعمال الشفوية (الفترة الأولى)' },
                oral2: { max: 5, label: 'الأعمال الشفوية (الفترة الثانية)' },
                practical1: { max: 20, label: 'الأنشطة العملية (الفترة الأولى)' },
                practical2: { max: 20, label: 'الأنشطة العملية (الفترة الثانية)' },
                project: { max: 20, label: 'المشروع' },
                shortTest: { max: 20, label: 'الاختبار القصير' },
                finalExam: { max: 30, label: 'الاختبار النهائي' },
                total: 100
            };
        }
        return {};
    }

    // Format date to Arabic
    static formatDateArabic(date) {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        };
        return new Date(date).toLocaleDateString('ar-SA', options);
    }

    // Format number to Arabic
    static formatNumberArabic(number) {
        return new Intl.NumberFormat('ar-SA').format(number);
    }

    // Validate student data
    static validateStudent(student) {
        const errors = [];

        if (!student.name || student.name.trim().length < 2) {
            errors.push('اسم الطالب مطلوب ويجب أن يكون أكثر من حرفين');
        }

        if (!student.studentId || student.studentId.trim().length < 1) {
            errors.push('رقم الطالب مطلوب');
        }

        if (!student.grade || student.grade < 1 || student.grade > 12) {
            errors.push('الصف الدراسي مطلوب ويجب أن يكون بين 1 و 12');
        }

        if (!student.section || student.section.trim().length < 1) {
            errors.push('الشعبة مطلوبة');
        }

        if (!student.schoolId) {
            errors.push('المدرسة مطلوبة');
        }

        return errors;
    }

    // Validate school data
    static validateSchool(school) {
        const errors = [];

        if (!school.name || school.name.trim().length < 2) {
            errors.push('اسم المدرسة مطلوب ويجب أن يكون أكثر من حرفين');
        }

        if (!school.code || school.code.trim().length < 1) {
            errors.push('رمز المدرسة مطلوب');
        }

        return errors;
    }

    // Validate grades
    static validateGrades(gradeLevel, scores) {
        const structure = this.getGradeStructure(gradeLevel);
        const errors = [];

        for (const [key, config] of Object.entries(structure)) {
            if (key === 'total') continue;
            
            const score = scores[key];
            if (score !== undefined && score !== null && score !== '') {
                const numScore = parseFloat(score);
                if (isNaN(numScore) || numScore < 0 || numScore > config.max) {
                    errors.push(`${config.label}: يجب أن تكون الدرجة بين 0 و ${config.max}`);
                }
            }
        }

        return errors;
    }

    // Generate Excel template for student import
    static generateStudentTemplate() {
        const headers = [
            'اسم الطالب',
            'رقم الطالب',
            'الصف الدراسي',
            'الشعبة',
            'تاريخ الميلاد',
            'ملاحظات'
        ];

        const sampleData = [
            ['أحمد محمد علي', '12345', '5', 'أ', '2010-01-15', ''],
            ['فاطمة أحمد سالم', '12346', '5', 'أ', '2010-03-20', ''],
            ['محمد سعد الدين', '12347', '5', 'ب', '2010-02-10', '']
        ];

        return {
            headers: headers,
            data: sampleData
        };
    }

    // Parse Excel file for student import
    static parseStudentExcel(data) {
        const students = [];
        const errors = [];

        // Skip header row
        for (let i = 1; i < data.length; i++) {
            const row = data[i];
            if (!row || row.length < 4) continue;

            const student = {
                name: row[0]?.toString().trim() || '',
                studentId: row[1]?.toString().trim() || '',
                grade: parseInt(row[2]) || 0,
                section: row[3]?.toString().trim() || '',
                birthDate: row[4] ? new Date(row[4]).toISOString().split('T')[0] : '',
                notes: row[5]?.toString().trim() || ''
            };

            const validationErrors = this.validateStudent(student);
            if (validationErrors.length > 0) {
                errors.push(`الصف ${i + 1}: ${validationErrors.join(', ')}`);
            } else {
                students.push(student);
            }
        }

        return { students, errors };
    }

    // Calculate statistics for a group of students
    static calculateStatistics(grades) {
        if (!grades || grades.length === 0) {
            return {
                total: 0,
                passed: 0,
                failed: 0,
                passRate: 0,
                average: 0,
                distribution: { أ: 0, ب: 0, ج: 0, د: 0, هـ: 0, راسب: 0 }
            };
        }

        const distribution = { أ: 0, ب: 0, ج: 0, د: 0, هـ: 0, راسب: 0 };
        let totalScore = 0;
        let passed = 0;

        grades.forEach(grade => {
            if (grade.level) {
                distribution[grade.level] = (distribution[grade.level] || 0) + 1;
                if (grade.level !== 'راسب' && grade.level !== 'ز') {
                    passed++;
                }
            }
            totalScore += grade.total || 0;
        });

        const average = grades.length > 0 ? totalScore / grades.length : 0;
        const passRate = grades.length > 0 ? (passed / grades.length) * 100 : 0;

        return {
            total: grades.length,
            passed: passed,
            failed: grades.length - passed,
            passRate: Math.round(passRate * 100) / 100,
            average: Math.round(average * 100) / 100,
            distribution: distribution
        };
    }

    // Show notification
    static showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Add close button functionality
        notification.querySelector('.notification-close').addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }

    static getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Show loading spinner
    static showLoading(container) {
        const loading = document.createElement('div');
        loading.className = 'loading-spinner';
        loading.innerHTML = `
            <div class="spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <span>جاري التحميل...</span>
            </div>
        `;
        container.appendChild(loading);
        return loading;
    }

    // Hide loading spinner
    static hideLoading(loadingElement) {
        if (loadingElement && loadingElement.parentNode) {
            loadingElement.parentNode.removeChild(loadingElement);
        }
    }

    // Debounce function for search
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Export data to Excel
    static exportToExcel(data, filename) {
        if (typeof XLSX !== 'undefined') {
            const ws = XLSX.utils.json_to_sheet(data);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'البيانات');
            XLSX.writeFile(wb, filename);
        } else {
            this.showNotification('مكتبة Excel غير متوفرة', 'error');
        }
    }

    // Print report
    static printReport(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>تقرير</title>
                    <style>
                        body { font-family: 'Cairo', sans-serif; direction: rtl; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background-color: #f2f2f2; }
                        @media print {
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    }
}
