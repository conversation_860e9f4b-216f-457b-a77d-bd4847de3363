/* RTL Support for Arabic Language */

/* Base RTL Styles */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .header-content {
    direction: rtl;
}

[dir="rtl"] .logo {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav {
    direction: rtl;
}

[dir="rtl"] .nav-btn {
    flex-direction: row-reverse;
}

[dir="rtl"] .section-header {
    direction: rtl;
}

[dir="rtl"] .section-header h2 {
    flex-direction: row-reverse;
}

[dir="rtl"] .dashboard-card {
    direction: rtl;
    flex-direction: row-reverse;
}

[dir="rtl"] .btn {
    flex-direction: row-reverse;
}

[dir="rtl"] .filters {
    direction: rtl;
}

[dir="rtl"] .table {
    direction: rtl;
}

[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

[dir="rtl"] .modal-header {
    direction: rtl;
}

[dir="rtl"] .modal-title {
    text-align: right;
}

/* Form RTL Styles */
[dir="rtl"] .form-group {
    direction: rtl;
}

[dir="rtl"] .form-label {
    text-align: right;
}

[dir="rtl"] .form-input,
[dir="rtl"] .form-select,
[dir="rtl"] .form-textarea {
    direction: rtl;
    text-align: right;
}

/* Navigation RTL */
[dir="rtl"] .section-actions {
    direction: rtl;
}

/* Dashboard RTL */
[dir="rtl"] .card-content {
    text-align: right;
}

[dir="rtl"] .card-content h3 {
    text-align: right;
}

/* Specific RTL adjustments */
[dir="rtl"] .logo i {
    margin-left: 0.5rem;
    margin-right: 0;
}

[dir="rtl"] .nav-btn i {
    margin-left: 0.5rem;
    margin-right: 0;
}

[dir="rtl"] .section-header h2 i {
    margin-left: 0.5rem;
    margin-right: 0;
}

[dir="rtl"] .btn i {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Table RTL specific */
[dir="rtl"] .table thead th:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 12px;
}

[dir="rtl"] .table thead th:last-child {
    border-top-left-radius: 0;
    border-top-right-radius: 12px;
}

/* Modal RTL */
[dir="rtl"] .modal-close {
    left: 1rem;
    right: auto;
}

/* Responsive RTL */
@media (max-width: 768px) {
    [dir="rtl"] .header-content {
        text-align: center;
    }
    
    [dir="rtl"] .nav {
        justify-content: center;
    }
    
    [dir="rtl"] .section-header {
        text-align: center;
    }
}

/* Arabic Font Optimization */
body[dir="rtl"] {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Number formatting for Arabic */
[dir="rtl"] .card-number {
    font-family: 'Cairo', monospace;
    direction: ltr;
    text-align: center;
}

/* Input placeholder RTL */
[dir="rtl"] input::placeholder,
[dir="rtl"] textarea::placeholder {
    text-align: right;
    direction: rtl;
}

/* Flex direction adjustments for RTL */
[dir="rtl"] .d-flex {
    flex-direction: row-reverse;
}

[dir="rtl"] .dashboard-grid {
    direction: rtl;
}

[dir="rtl"] .filters {
    flex-direction: row-reverse;
}

/* Chart container RTL */
[dir="rtl"] .chart-container {
    direction: ltr;
}

/* Statistics RTL */
[dir="rtl"] .statistics-container {
    direction: rtl;
}

[dir="rtl"] .stat-card {
    direction: rtl;
    text-align: right;
}

/* Reports RTL */
[dir="rtl"] .reports-container {
    direction: rtl;
}

[dir="rtl"] .report-card {
    direction: rtl;
    text-align: right;
}

/* Grades table RTL */
[dir="rtl"] .grades-container {
    direction: rtl;
}

[dir="rtl"] .grades-table {
    direction: rtl;
}

[dir="rtl"] .grades-table th,
[dir="rtl"] .grades-table td {
    text-align: center;
}

[dir="rtl"] .grades-table .student-name {
    text-align: right;
}

/* Students table RTL */
[dir="rtl"] .students-table {
    direction: rtl;
}

[dir="rtl"] .students-table th,
[dir="rtl"] .students-table td {
    text-align: right;
}

[dir="rtl"] .students-table .student-id,
[dir="rtl"] .students-table .student-grade {
    text-align: center;
}

/* Schools RTL */
[dir="rtl"] .schools-container {
    direction: rtl;
}

[dir="rtl"] .school-card {
    direction: rtl;
    text-align: right;
}

/* Action buttons RTL */
[dir="rtl"] .action-buttons {
    direction: rtl;
    justify-content: flex-start;
}

/* Pagination RTL */
[dir="rtl"] .pagination {
    direction: rtl;
    flex-direction: row-reverse;
}

/* Search RTL */
[dir="rtl"] .search-container {
    direction: rtl;
}

[dir="rtl"] .search-input {
    text-align: right;
    direction: rtl;
}

/* Loading spinner RTL */
[dir="rtl"] .loading-spinner {
    direction: ltr;
}

/* Breadcrumb RTL */
[dir="rtl"] .breadcrumb {
    direction: rtl;
    flex-direction: row-reverse;
}

[dir="rtl"] .breadcrumb-item::after {
    content: "\\";
    margin: 0 0.5rem;
}

/* Dropdown RTL */
[dir="rtl"] .dropdown {
    direction: rtl;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

/* Tooltip RTL */
[dir="rtl"] .tooltip {
    direction: rtl;
    text-align: right;
}

/* Progress bar RTL */
[dir="rtl"] .progress {
    direction: ltr;
}

[dir="rtl"] .progress-label {
    direction: rtl;
    text-align: right;
}

/* Alert RTL */
[dir="rtl"] .alert {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] .alert-icon {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Tab navigation RTL */
[dir="rtl"] .tab-nav {
    direction: rtl;
    flex-direction: row-reverse;
}

[dir="rtl"] .tab-content {
    direction: rtl;
    text-align: right;
}

/* Card RTL */
[dir="rtl"] .card {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] .card-header {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] .card-body {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] .card-footer {
    direction: rtl;
    text-align: right;
}
