// Reports management system

class ReportsManager {
    constructor() {
        this.students = [];
        this.schools = [];
        this.grades = [];
        this.currentFilters = {
            school: '',
            grade: '',
            section: '',
            semester: '',
            academicYear: '2024-2025'
        };
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Report type buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.report-type-btn')) {
                this.generateReport(e.target.dataset.reportType);
            }
        });
    }

    async initialize() {
        try {
            [this.students, this.schools, this.grades] = await Promise.all([
                db.getStudents(),
                db.getSchools(),
                db.getGrades()
            ]);

            this.renderReportsInterface();
        } catch (error) {
            console.error('Error initializing reports manager:', error);
            Utils.showNotification('خطأ في تحميل البيانات', 'error');
        }
    }

    renderReportsInterface() {
        const container = document.querySelector('#reports .reports-container');
        if (!container) return;

        container.innerHTML = `
            <div class="reports-dashboard">
                <div class="report-filters">
                    <h3>فلاتر التقارير</h3>
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label>المدرسة</label>
                            <select id="reportSchoolFilter" class="form-select">
                                <option value="">جميع المدارس</option>
                                ${this.schools.map(school =>
                                    `<option value="${school.id}">${school.name}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>الصف الدراسي</label>
                            <select id="reportGradeFilter" class="form-select">
                                <option value="">جميع الصفوف</option>
                                ${this.generateGradeOptions()}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>الشعبة</label>
                            <select id="reportSectionFilter" class="form-select">
                                <option value="">جميع الشعب</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>الفصل الدراسي</label>
                            <select id="reportSemesterFilter" class="form-select">
                                <option value="">كلا الفصلين</option>
                                <option value="1">الفصل الأول</option>
                                <option value="2">الفصل الثاني</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>العام الدراسي</label>
                            <select id="reportAcademicYearFilter" class="form-select">
                                <option value="2024-2025">2024-2025</option>
                                <option value="2023-2024">2023-2024</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="report-types">
                    <h3>أنواع التقارير</h3>
                    <div class="report-types-grid">
                        <div class="report-type-card">
                            <div class="report-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h4>تقرير الدرجات الفصلي</h4>
                            <p>تقرير شامل لدرجات الطلاب في فصل دراسي محدد</p>
                            <button class="btn btn-primary report-type-btn" data-report-type="semester">
                                إنشاء التقرير
                            </button>
                        </div>

                        <div class="report-type-card">
                            <div class="report-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <h4>التقرير السنوي</h4>
                            <p>تقرير شامل لأداء الطلاب خلال العام الدراسي</p>
                            <button class="btn btn-primary report-type-btn" data-report-type="annual">
                                إنشاء التقرير
                            </button>
                        </div>

                        <div class="report-type-card">
                            <div class="report-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h4>تقرير الطلاب</h4>
                            <p>قائمة بالطلاب وبياناتهم الأساسية</p>
                            <button class="btn btn-primary report-type-btn" data-report-type="students">
                                إنشاء التقرير
                            </button>
                        </div>

                        <div class="report-type-card">
                            <div class="report-icon">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <h4>تقرير الإحصائيات</h4>
                            <p>إحصائيات مفصلة عن الأداء والتوزيع</p>
                            <button class="btn btn-primary report-type-btn" data-report-type="statistics">
                                إنشاء التقرير
                            </button>
                        </div>

                        <div class="report-type-card">
                            <div class="report-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <h4>تقرير المتفوقين</h4>
                            <p>قائمة بالطلاب المتفوقين والحاصلين على أعلى الدرجات</p>
                            <button class="btn btn-primary report-type-btn" data-report-type="topStudents">
                                إنشاء التقرير
                            </button>
                        </div>

                        <div class="report-type-card">
                            <div class="report-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <h4>تقرير الطلاب المتعثرين</h4>
                            <p>قائمة بالطلاب الذين يحتاجون لمتابعة إضافية</p>
                            <button class="btn btn-primary report-type-btn" data-report-type="struggling">
                                إنشاء التقرير
                            </button>
                        </div>
                    </div>
                </div>

                <div id="reportOutput" class="report-output">
                    <!-- Generated reports will appear here -->
                </div>
            </div>
        `;

        this.addFilterListeners();
    }

    addFilterListeners() {
        // School filter
        document.getElementById('reportSchoolFilter').addEventListener('change', (e) => {
            this.currentFilters.school = e.target.value;
            this.updateSectionFilter();
        });

        // Grade filter
        document.getElementById('reportGradeFilter').addEventListener('change', (e) => {
            this.currentFilters.grade = e.target.value;
            this.updateSectionFilter();
        });

        // Other filters
        ['reportSectionFilter', 'reportSemesterFilter', 'reportAcademicYearFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    const filterKey = id.replace('report', '').replace('Filter', '').toLowerCase();
                    this.currentFilters[filterKey] = e.target.value;
                });
            }
        });
    }

    updateSectionFilter() {
        const sectionFilter = document.getElementById('reportSectionFilter');
        if (!sectionFilter) return;

        // Get unique sections based on current filters
        let filteredStudents = this.students;

        if (this.currentFilters.school) {
            filteredStudents = filteredStudents.filter(s => s.schoolId === this.currentFilters.school);
        }

        if (this.currentFilters.grade) {
            filteredStudents = filteredStudents.filter(s => s.grade === parseInt(this.currentFilters.grade));
        }

        const sections = [...new Set(filteredStudents.map(s => s.section))].sort();

        sectionFilter.innerHTML = '<option value="">جميع الشعب</option>';
        sections.forEach(section => {
            const option = document.createElement('option');
            option.value = section;
            option.textContent = section;
            sectionFilter.appendChild(option);
        });
    }

    generateGradeOptions() {
        let options = '';
        for (let i = 1; i <= 12; i++) {
            options += `<option value="${i}">الصف ${this.getGradeName(i)}</option>`;
        }
        return options;
    }

    async generateReport(reportType) {
        try {
            const loading = Utils.showLoading(document.getElementById('reportOutput'));

            let reportHTML = '';

            switch (reportType) {
                case 'semester':
                    reportHTML = await this.generateSemesterReport();
                    break;
                case 'annual':
                    reportHTML = await this.generateAnnualReport();
                    break;
                case 'students':
                    reportHTML = await this.generateStudentsReport();
                    break;
                case 'statistics':
                    reportHTML = await this.generateStatisticsReport();
                    break;
                case 'topStudents':
                    reportHTML = await this.generateTopStudentsReport();
                    break;
                case 'struggling':
                    reportHTML = await this.generateStrugglingStudentsReport();
                    break;
                default:
                    reportHTML = '<p>نوع التقرير غير مدعوم</p>';
            }

            document.getElementById('reportOutput').innerHTML = reportHTML;
            Utils.hideLoading(loading);

        } catch (error) {
            console.error('Error generating report:', error);
            Utils.showNotification('خطأ في إنشاء التقرير', 'error');
        }
    }

    async generateSemesterReport() {
        const filteredData = this.getFilteredData();

        if (filteredData.length === 0) {
            return '<div class="empty-state"><p>لا توجد بيانات للتقرير المطلوب</p></div>';
        }

        const statistics = Utils.calculateStatistics(filteredData.map(item => item.calculated));

        return `
            <div class="report-container" id="semesterReport">
                <div class="report-header">
                    <h2>تقرير الدرجات الفصلي</h2>
                    <div class="report-info">
                        <p><strong>الفصل الدراسي:</strong> ${this.currentFilters.semester ? (this.currentFilters.semester === '1' ? 'الأول' : 'الثاني') : 'كلا الفصلين'}</p>
                        <p><strong>العام الدراسي:</strong> ${this.currentFilters.academicyear || '2024-2025'}</p>
                        <p><strong>تاريخ التقرير:</strong> ${Utils.formatDateArabic(new Date())}</p>
                    </div>
                </div>

                <div class="report-summary">
                    <h3>ملخص الإحصائيات</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="label">إجمالي الطلاب:</span>
                            <span class="value">${statistics.total}</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">الناجحون:</span>
                            <span class="value">${statistics.passed}</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">نسبة النجاح:</span>
                            <span class="value">${statistics.passRate}%</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">المتوسط العام:</span>
                            <span class="value">${statistics.average}</span>
                        </div>
                    </div>
                </div>

                <div class="report-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الطالب</th>
                                <th>رقم الطالب</th>
                                <th>المدرسة</th>
                                <th>الصف</th>
                                <th>الشعبة</th>
                                <th>المجموع</th>
                                <th>المستوى</th>
                                <th>العبارة الوصفية</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${filteredData.map(item => `
                                <tr>
                                    <td>${item.student.name}</td>
                                    <td>${item.student.studentId}</td>
                                    <td>${item.school.name}</td>
                                    <td>${this.getGradeName(item.student.grade)}</td>
                                    <td>${item.student.section}</td>
                                    <td>${item.calculated.total}</td>
                                    <td>${item.calculated.level}</td>
                                    <td>${item.calculated.description}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                ${this.generateReportFooter(this.currentFilters.school)}

                <div class="report-actions no-print">
                    <button class="btn btn-primary" onclick="Utils.printReport('semesterReport')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="reportsManager.exportReport('semester')">
                        <i class="fas fa-download"></i> تصدير Excel
                    </button>
                </div>
            </div>
        `;
    }

    async generateAnnualReport() {
        // Get data for both semesters
        const semester1Data = this.getFilteredData(1);
        const semester2Data = this.getFilteredData(2);

        const combinedData = this.combineAnnualData(semester1Data, semester2Data);

        if (combinedData.length === 0) {
            return '<div class="empty-state"><p>لا توجد بيانات للتقرير السنوي</p></div>';
        }

        const statistics = Utils.calculateStatistics(combinedData.map(item => item.annual));

        return `
            <div class="report-container" id="annualReport">
                <div class="report-header">
                    <h2>التقرير السنوي</h2>
                    <div class="report-info">
                        <p><strong>العام الدراسي:</strong> ${this.currentFilters.academicyear || '2024-2025'}</p>
                        <p><strong>تاريخ التقرير:</strong> ${Utils.formatDateArabic(new Date())}</p>
                    </div>
                </div>

                <div class="report-summary">
                    <h3>ملخص الأداء السنوي</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="label">إجمالي الطلاب:</span>
                            <span class="value">${statistics.total}</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">المتوسط السنوي:</span>
                            <span class="value">${statistics.average}</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">نسبة النجاح:</span>
                            <span class="value">${statistics.passRate}%</span>
                        </div>
                    </div>
                </div>

                <div class="report-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th rowspan="2">اسم الطالب</th>
                                <th rowspan="2">رقم الطالب</th>
                                <th colspan="3">الفصل الأول</th>
                                <th colspan="3">الفصل الثاني</th>
                                <th colspan="3">المتوسط السنوي</th>
                            </tr>
                            <tr>
                                <th>المجموع</th>
                                <th>المستوى</th>
                                <th>الوصف</th>
                                <th>المجموع</th>
                                <th>المستوى</th>
                                <th>الوصف</th>
                                <th>المجموع</th>
                                <th>المستوى</th>
                                <th>الوصف</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${combinedData.map(item => `
                                <tr>
                                    <td>${item.student.name}</td>
                                    <td>${item.student.studentId}</td>
                                    <td>${item.semester1?.total || '-'}</td>
                                    <td>${item.semester1?.level || '-'}</td>
                                    <td>${item.semester1?.description || '-'}</td>
                                    <td>${item.semester2?.total || '-'}</td>
                                    <td>${item.semester2?.level || '-'}</td>
                                    <td>${item.semester2?.description || '-'}</td>
                                    <td>${item.annual.total}</td>
                                    <td>${item.annual.level}</td>
                                    <td>${item.annual.description}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                ${this.generateReportFooter(this.currentFilters.school)}

                <div class="report-actions no-print">
                    <button class="btn btn-primary" onclick="Utils.printReport('annualReport')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="reportsManager.exportReport('annual')">
                        <i class="fas fa-download"></i> تصدير Excel
                    </button>
                </div>
            </div>
        `;
    }

    async generateStudentsReport() {
        const filteredStudents = this.getFilteredStudents();

        if (filteredStudents.length === 0) {
            return '<div class="empty-state"><p>لا توجد طلاب للتقرير المطلوب</p></div>';
        }

        return `
            <div class="report-container" id="studentsReport">
                <div class="report-header">
                    <h2>تقرير الطلاب</h2>
                    <div class="report-info">
                        <p><strong>إجمالي الطلاب:</strong> ${filteredStudents.length}</p>
                        <p><strong>تاريخ التقرير:</strong> ${Utils.formatDateArabic(new Date())}</p>
                    </div>
                </div>

                <div class="report-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الطالب</th>
                                <th>رقم الطالب</th>
                                <th>المدرسة</th>
                                <th>الصف الدراسي</th>
                                <th>الشعبة</th>
                                <th>تاريخ الميلاد</th>
                                <th>تاريخ التسجيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${filteredStudents.map(student => {
                                const school = this.schools.find(s => s.id === student.schoolId);
                                return `
                                    <tr>
                                        <td>${student.name}</td>
                                        <td>${student.studentId}</td>
                                        <td>${school ? school.name : 'غير محدد'}</td>
                                        <td>${this.getGradeName(student.grade)}</td>
                                        <td>${student.section}</td>
                                        <td>${student.birthDate ? Utils.formatDateArabic(student.birthDate) : '-'}</td>
                                        <td>${Utils.formatDateArabic(student.createdAt)}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>

                ${this.generateReportFooter(this.currentFilters.school)}

                <div class="report-actions no-print">
                    <button class="btn btn-primary" onclick="Utils.printReport('studentsReport')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="reportsManager.exportReport('students')">
                        <i class="fas fa-download"></i> تصدير Excel
                    </button>
                </div>
            </div>
        `;
    }

    async generateStatisticsReport() {
        const filteredData = this.getFilteredData();

        if (filteredData.length === 0) {
            return '<div class="empty-state"><p>لا توجد بيانات للإحصائيات</p></div>';
        }

        const statistics = Utils.calculateStatistics(filteredData.map(item => item.calculated));

        // Calculate additional statistics
        const gradeDistribution = this.calculateGradeDistribution(filteredData);
        const schoolComparison = this.calculateSchoolComparison(filteredData);

        return `
            <div class="report-container" id="statisticsReport">
                <div class="report-header">
                    <h2>تقرير الإحصائيات المفصل</h2>
                    <div class="report-info">
                        <p><strong>تاريخ التقرير:</strong> ${Utils.formatDateArabic(new Date())}</p>
                    </div>
                </div>

                <div class="statistics-grid">
                    <div class="stat-section">
                        <h3>الإحصائيات العامة</h3>
                        <div class="stats-cards">
                            <div class="stat-card">
                                <h4>إجمالي الطلاب</h4>
                                <span class="stat-number">${statistics.total}</span>
                            </div>
                            <div class="stat-card">
                                <h4>المتوسط العام</h4>
                                <span class="stat-number">${statistics.average}</span>
                            </div>
                            <div class="stat-card">
                                <h4>نسبة النجاح</h4>
                                <span class="stat-number">${statistics.passRate}%</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-section">
                        <h3>توزيع المستويات</h3>
                        <div class="distribution-chart">
                            ${Object.entries(statistics.distribution).map(([level, count]) => `
                                <div class="distribution-item">
                                    <span class="level">${level}</span>
                                    <div class="bar-container">
                                        <div class="bar" style="width: ${statistics.total > 0 ? (count / statistics.total) * 100 : 0}%"></div>
                                    </div>
                                    <span class="count">${count} (${statistics.total > 0 ? Math.round((count / statistics.total) * 100) : 0}%)</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    ${schoolComparison.length > 1 ? `
                        <div class="stat-section">
                            <h3>مقارنة المدارس</h3>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>المدرسة</th>
                                        <th>عدد الطلاب</th>
                                        <th>المتوسط</th>
                                        <th>نسبة النجاح</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${schoolComparison.map(school => `
                                        <tr>
                                            <td>${school.name}</td>
                                            <td>${school.studentCount}</td>
                                            <td>${school.average}</td>
                                            <td>${school.passRate}%</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    ` : ''}
                </div>

                ${this.generateReportFooter(this.currentFilters.school)}

                <div class="report-actions no-print">
                    <button class="btn btn-primary" onclick="Utils.printReport('statisticsReport')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="reportsManager.exportReport('statistics')">
                        <i class="fas fa-download"></i> تصدير Excel
                    </button>
                </div>
            </div>
        `;
    }

    async generateTopStudentsReport() {
        const filteredData = this.getFilteredData();

        if (filteredData.length === 0) {
            return '<div class="empty-state"><p>لا توجد بيانات للطلاب المتفوقين</p></div>';
        }

        // Sort by total score descending and take top 20%
        const sortedData = filteredData.sort((a, b) => b.calculated.total - a.calculated.total);
        const topCount = Math.max(5, Math.ceil(sortedData.length * 0.2));
        const topStudents = sortedData.slice(0, topCount);

        return `
            <div class="report-container" id="topStudentsReport">
                <div class="report-header">
                    <h2>تقرير الطلاب المتفوقين</h2>
                    <div class="report-info">
                        <p><strong>عدد الطلاب المتفوقين:</strong> ${topStudents.length}</p>
                        <p><strong>من إجمالي:</strong> ${filteredData.length} طالب</p>
                        <p><strong>تاريخ التقرير:</strong> ${Utils.formatDateArabic(new Date())}</p>
                    </div>
                </div>

                <div class="report-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الترتيب</th>
                                <th>اسم الطالب</th>
                                <th>رقم الطالب</th>
                                <th>المدرسة</th>
                                <th>الصف</th>
                                <th>الشعبة</th>
                                <th>المجموع</th>
                                <th>المستوى</th>
                                <th>النسبة المئوية</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${topStudents.map((item, index) => {
                                const percentage = (item.calculated.total / Utils.getGradeStructure(item.student.grade).total * 100).toFixed(1);
                                return `
                                    <tr class="top-student-row">
                                        <td class="rank">${index + 1}</td>
                                        <td>${item.student.name}</td>
                                        <td>${item.student.studentId}</td>
                                        <td>${item.school.name}</td>
                                        <td>${this.getGradeName(item.student.grade)}</td>
                                        <td>${item.student.section}</td>
                                        <td class="score">${item.calculated.total}</td>
                                        <td class="level">${item.calculated.level}</td>
                                        <td class="percentage">${percentage}%</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>

                ${this.generateReportFooter(this.currentFilters.school)}

                <div class="report-actions no-print">
                    <button class="btn btn-primary" onclick="Utils.printReport('topStudentsReport')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="reportsManager.exportReport('topStudents')">
                        <i class="fas fa-download"></i> تصدير Excel
                    </button>
                </div>
            </div>
        `;
    }

    async generateStrugglingStudentsReport() {
        const filteredData = this.getFilteredData();

        if (filteredData.length === 0) {
            return '<div class="empty-state"><p>لا توجد بيانات للطلاب المتعثرين</p></div>';
        }

        // Filter students who are struggling (failed or low grades)
        const strugglingStudents = filteredData.filter(item => {
            const level = item.calculated.level;
            const total = item.calculated.total;
            const maxTotal = Utils.getGradeStructure(item.student.grade).total;
            const percentage = (total / maxTotal) * 100;

            return level === 'راسب' || level === 'هـ' || level === 'ز' || percentage < 60;
        });

        return `
            <div class="report-container" id="strugglingStudentsReport">
                <div class="report-header">
                    <h2>تقرير الطلاب المتعثرين</h2>
                    <div class="report-info">
                        <p><strong>عدد الطلاب المتعثرين:</strong> ${strugglingStudents.length}</p>
                        <p><strong>من إجمالي:</strong> ${filteredData.length} طالب</p>
                        <p><strong>نسبة التعثر:</strong> ${((strugglingStudents.length / filteredData.length) * 100).toFixed(1)}%</p>
                        <p><strong>تاريخ التقرير:</strong> ${Utils.formatDateArabic(new Date())}</p>
                    </div>
                </div>

                ${strugglingStudents.length === 0 ? `
                    <div class="success-message">
                        <i class="fas fa-check-circle"></i>
                        <h3>ممتاز! لا يوجد طلاب متعثرون</h3>
                        <p>جميع الطلاب يحققون أداءً مقبولاً أو أفضل</p>
                    </div>
                ` : `
                    <div class="report-table">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>اسم الطالب</th>
                                    <th>رقم الطالب</th>
                                    <th>المدرسة</th>
                                    <th>الصف</th>
                                    <th>الشعبة</th>
                                    <th>المجموع</th>
                                    <th>المستوى</th>
                                    <th>النسبة المئوية</th>
                                    <th>التوصيات</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${strugglingStudents.map(item => {
                                    const maxTotal = Utils.getGradeStructure(item.student.grade).total;
                                    const percentage = (item.calculated.total / maxTotal * 100).toFixed(1);
                                    const recommendations = this.getRecommendations(item.calculated.level, percentage);

                                    return `
                                        <tr class="struggling-student-row">
                                            <td>${item.student.name}</td>
                                            <td>${item.student.studentId}</td>
                                            <td>${item.school.name}</td>
                                            <td>${this.getGradeName(item.student.grade)}</td>
                                            <td>${item.student.section}</td>
                                            <td class="score">${item.calculated.total}</td>
                                            <td class="level">${item.calculated.level}</td>
                                            <td class="percentage">${percentage}%</td>
                                            <td class="recommendations">${recommendations}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="recommendations-section">
                        <h3>التوصيات العامة</h3>
                        <ul class="recommendations-list">
                            <li>توفير دروس تقوية إضافية للطلاب المتعثرين</li>
                            <li>متابعة فردية مع أولياء الأمور</li>
                            <li>تطبيق استراتيجيات تعليم متنوعة</li>
                            <li>إجراء تقييم دوري لمتابعة التحسن</li>
                            <li>توفير مواد تعليمية إضافية</li>
                        </ul>
                    </div>
                `}

                ${this.generateReportFooter(this.currentFilters.school)}

                <div class="report-actions no-print">
                    <button class="btn btn-primary" onclick="Utils.printReport('strugglingStudentsReport')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                    <button class="btn btn-secondary" onclick="reportsManager.exportReport('struggling')">
                        <i class="fas fa-download"></i> تصدير Excel
                    </button>
                </div>
            </div>
        `;
    }

    // Helper methods
    getFilteredData(semester = null) {
        let filteredGrades = this.grades;

        // Apply filters
        if (this.currentFilters.school) {
            filteredGrades = filteredGrades.filter(g => g.schoolId === this.currentFilters.school);
        }
        if (this.currentFilters.grade) {
            filteredGrades = filteredGrades.filter(g => g.grade === parseInt(this.currentFilters.grade));
        }
        if (this.currentFilters.section) {
            filteredGrades = filteredGrades.filter(g => g.section === this.currentFilters.section);
        }
        if (semester !== null) {
            filteredGrades = filteredGrades.filter(g => g.semester === semester);
        } else if (this.currentFilters.semester) {
            filteredGrades = filteredGrades.filter(g => g.semester === parseInt(this.currentFilters.semester));
        }
        if (this.currentFilters.academicyear) {
            filteredGrades = filteredGrades.filter(g => g.academicYear === this.currentFilters.academicyear);
        }

        // Combine with student and school data
        return filteredGrades.map(grade => {
            const student = this.students.find(s => s.id === grade.studentId);
            const school = this.schools.find(s => s.id === grade.schoolId);
            const calculated = Utils.calculateGrades(grade.grade, grade.grades);

            return {
                grade,
                student,
                school,
                calculated
            };
        }).filter(item => item.student && item.school);
    }

    getFilteredStudents() {
        let filteredStudents = this.students;

        if (this.currentFilters.school) {
            filteredStudents = filteredStudents.filter(s => s.schoolId === this.currentFilters.school);
        }
        if (this.currentFilters.grade) {
            filteredStudents = filteredStudents.filter(s => s.grade === parseInt(this.currentFilters.grade));
        }
        if (this.currentFilters.section) {
            filteredStudents = filteredStudents.filter(s => s.section === this.currentFilters.section);
        }

        return filteredStudents;
    }

    combineAnnualData(semester1Data, semester2Data) {
        const studentMap = new Map();

        // Add semester 1 data
        semester1Data.forEach(item => {
            studentMap.set(item.student.id, {
                student: item.student,
                school: item.school,
                semester1: item.calculated
            });
        });

        // Add semester 2 data
        semester2Data.forEach(item => {
            const existing = studentMap.get(item.student.id);
            if (existing) {
                existing.semester2 = item.calculated;
            } else {
                studentMap.set(item.student.id, {
                    student: item.student,
                    school: item.school,
                    semester2: item.calculated
                });
            }
        });

        // Calculate annual averages
        return Array.from(studentMap.values()).map(item => {
            const sem1Total = item.semester1?.total || 0;
            const sem2Total = item.semester2?.total || 0;
            const count = (item.semester1 ? 1 : 0) + (item.semester2 ? 1 : 0);

            if (count === 0) return null;

            const annualTotal = count === 2 ? (sem1Total + sem2Total) / 2 : (sem1Total || sem2Total);
            const annual = Utils.calculateGrades(item.student.grade, { total: annualTotal });
            annual.total = Math.round(annualTotal * 100) / 100;

            return {
                ...item,
                annual
            };
        }).filter(item => item !== null);
    }

    calculateGradeDistribution(data) {
        const distribution = {};

        data.forEach(item => {
            const grade = item.student.grade;
            if (!distribution[grade]) {
                distribution[grade] = {
                    grade: grade,
                    gradeName: this.getGradeName(grade),
                    count: 0,
                    average: 0,
                    totalScore: 0
                };
            }
            distribution[grade].count++;
            distribution[grade].totalScore += item.calculated.total;
        });

        // Calculate averages
        Object.values(distribution).forEach(item => {
            item.average = Math.round((item.totalScore / item.count) * 100) / 100;
        });

        return Object.values(distribution).sort((a, b) => a.grade - b.grade);
    }

    calculateSchoolComparison(data) {
        const schoolMap = new Map();

        data.forEach(item => {
            const schoolId = item.school.id;
            if (!schoolMap.has(schoolId)) {
                schoolMap.set(schoolId, {
                    id: schoolId,
                    name: item.school.name,
                    studentCount: 0,
                    totalScore: 0,
                    passedCount: 0
                });
            }

            const school = schoolMap.get(schoolId);
            school.studentCount++;
            school.totalScore += item.calculated.total;

            if (item.calculated.level !== 'راسب' && item.calculated.level !== 'ز') {
                school.passedCount++;
            }
        });

        return Array.from(schoolMap.values()).map(school => ({
            ...school,
            average: Math.round((school.totalScore / school.studentCount) * 100) / 100,
            passRate: Math.round((school.passedCount / school.studentCount) * 100 * 100) / 100
        })).sort((a, b) => b.average - a.average);
    }

    getRecommendations(level, percentage) {
        if (level === 'راسب' || percentage < 50) {
            return 'يحتاج لمتابعة مكثفة ودروس تقوية';
        } else if (level === 'هـ' || percentage < 60) {
            return 'يحتاج لمتابعة إضافية وتحسين الأداء';
        } else if (level === 'د' || percentage < 70) {
            return 'يحتاج لمراجعة المفاهيم الأساسية';
        }
        return 'متابعة عادية';
    }

    getGradeName(grade) {
        const names = {
            1: 'الأول', 2: 'الثاني', 3: 'الثالث', 4: 'الرابع',
            5: 'الخامس', 6: 'السادس', 7: 'السابع', 8: 'الثامن',
            9: 'التاسع', 10: 'العاشر', 11: 'الحادي عشر', 12: 'الثاني عشر'
        };
        return names[grade] || grade.toString();
    }

    generateReportFooter(schoolId) {
        const school = this.schools.find(s => s.id === schoolId);
        if (!school) return '';

        const currentDate = new Date().toLocaleDateString('ar-SA');
        const settings = window.app?.settings || {};
        const reportFooter = settings.reportSettings?.reportFooter || 'تم إنشاء هذا التقرير بواسطة نظام تقويم تقنية المعلومات';

        return `
            <div class="report-footer">
                <div class="staff-signatures">
                    <div class="signature-section">
                        <div class="signature-line">
                            <span class="signature-label">معلم المادة:</span>
                            <span class="signature-name">${school.teacherName || '___________________'}</span>
                        </div>
                        <div class="signature-space">التوقيع: ___________________</div>
                    </div>

                    <div class="signature-section">
                        <div class="signature-line">
                            <span class="signature-label">المشرف التربوي:</span>
                            <span class="signature-name">${school.supervisorName || '___________________'}</span>
                        </div>
                        <div class="signature-space">التوقيع: ___________________</div>
                    </div>

                    <div class="signature-section">
                        <div class="signature-line">
                            <span class="signature-label">مدير المدرسة:</span>
                            <span class="signature-name">${school.principalName || '___________________'}</span>
                        </div>
                        <div class="signature-space">التوقيع: ___________________</div>
                    </div>
                </div>

                <div class="report-meta">
                    <div class="report-date">تاريخ إنشاء التقرير: ${currentDate}</div>
                    <div class="report-system">${reportFooter}</div>
                </div>
            </div>
        `;
    }

    async exportReport(reportType) {
        try {
            let data = [];
            let filename = '';

            switch (reportType) {
                case 'semester':
                    data = this.getFilteredData().map(item => ({
                        'اسم الطالب': item.student.name,
                        'رقم الطالب': item.student.studentId,
                        'المدرسة': item.school.name,
                        'الصف': this.getGradeName(item.student.grade),
                        'الشعبة': item.student.section,
                        'المجموع': item.calculated.total,
                        'المستوى': item.calculated.level,
                        'العبارة الوصفية': item.calculated.description
                    }));
                    filename = `تقرير_فصلي_${new Date().toISOString().split('T')[0]}.xlsx`;
                    break;

                case 'annual':
                    const semester1Data = this.getFilteredData(1);
                    const semester2Data = this.getFilteredData(2);
                    const combinedData = this.combineAnnualData(semester1Data, semester2Data);

                    data = combinedData.map(item => ({
                        'اسم الطالب': item.student.name,
                        'رقم الطالب': item.student.studentId,
                        'المدرسة': item.school.name,
                        'الصف': this.getGradeName(item.student.grade),
                        'الشعبة': item.student.section,
                        'الفصل الأول - المجموع': item.semester1?.total || '-',
                        'الفصل الأول - المستوى': item.semester1?.level || '-',
                        'الفصل الثاني - المجموع': item.semester2?.total || '-',
                        'الفصل الثاني - المستوى': item.semester2?.level || '-',
                        'المتوسط السنوي': item.annual.total,
                        'المستوى السنوي': item.annual.level
                    }));
                    filename = `التقرير_السنوي_${new Date().toISOString().split('T')[0]}.xlsx`;
                    break;

                case 'students':
                    data = this.getFilteredStudents().map(student => {
                        const school = this.schools.find(s => s.id === student.schoolId);
                        return {
                            'اسم الطالب': student.name,
                            'رقم الطالب': student.studentId,
                            'المدرسة': school ? school.name : 'غير محدد',
                            'الصف الدراسي': this.getGradeName(student.grade),
                            'الشعبة': student.section,
                            'تاريخ الميلاد': student.birthDate || '',
                            'تاريخ التسجيل': Utils.formatDateArabic(student.createdAt)
                        };
                    });
                    filename = `تقرير_الطلاب_${new Date().toISOString().split('T')[0]}.xlsx`;
                    break;

                case 'topStudents':
                    const filteredData = this.getFilteredData();
                    const sortedData = filteredData.sort((a, b) => b.calculated.total - a.calculated.total);
                    const topCount = Math.max(5, Math.ceil(sortedData.length * 0.2));
                    const topStudents = sortedData.slice(0, topCount);

                    data = topStudents.map((item, index) => ({
                        'الترتيب': index + 1,
                        'اسم الطالب': item.student.name,
                        'رقم الطالب': item.student.studentId,
                        'المدرسة': item.school.name,
                        'الصف': this.getGradeName(item.student.grade),
                        'الشعبة': item.student.section,
                        'المجموع': item.calculated.total,
                        'المستوى': item.calculated.level,
                        'النسبة المئوية': (item.calculated.total / Utils.getGradeStructure(item.student.grade).total * 100).toFixed(1) + '%'
                    }));
                    filename = `الطلاب_المتفوقين_${new Date().toISOString().split('T')[0]}.xlsx`;
                    break;

                case 'struggling':
                    const strugglingData = this.getFilteredData().filter(item => {
                        const level = item.calculated.level;
                        const total = item.calculated.total;
                        const maxTotal = Utils.getGradeStructure(item.student.grade).total;
                        const percentage = (total / maxTotal) * 100;

                        return level === 'راسب' || level === 'هـ' || level === 'ز' || percentage < 60;
                    });

                    data = strugglingData.map(item => ({
                        'اسم الطالب': item.student.name,
                        'رقم الطالب': item.student.studentId,
                        'المدرسة': item.school.name,
                        'الصف': this.getGradeName(item.student.grade),
                        'الشعبة': item.student.section,
                        'المجموع': item.calculated.total,
                        'المستوى': item.calculated.level,
                        'النسبة المئوية': (item.calculated.total / Utils.getGradeStructure(item.student.grade).total * 100).toFixed(1) + '%',
                        'التوصيات': this.getRecommendations(item.calculated.level, (item.calculated.total / Utils.getGradeStructure(item.student.grade).total * 100))
                    }));
                    filename = `الطلاب_المتعثرين_${new Date().toISOString().split('T')[0]}.xlsx`;
                    break;

                default:
                    Utils.showNotification('نوع التقرير غير مدعوم للتصدير', 'error');
                    return;
            }

            if (data.length === 0) {
                Utils.showNotification('لا توجد بيانات للتصدير', 'warning');
                return;
            }

            Utils.exportToExcel(data, filename);
            Utils.showNotification('تم تصدير التقرير بنجاح', 'success');

        } catch (error) {
            console.error('Error exporting report:', error);
            Utils.showNotification('خطأ في تصدير التقرير', 'error');
        }
    }
}

// Create global reports manager instance
const reportsManager = new ReportsManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize reports manager when reports section is first shown
    const reportsSection = document.getElementById('reports');
    if (reportsSection) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (reportsSection.classList.contains('active') && !reportsManager.initialized) {
                        reportsManager.initialize();
                        reportsManager.initialized = true;
                    }
                }
            });
        });

        observer.observe(reportsSection, { attributes: true });
    }
});

// Make reportsManager available globally
window.reportsManager = reportsManager;