const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
    require('electron-reload')(__dirname, {
        electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
        hardResetMethod: 'exit'
    });
}

class ITEvaluationApp {
    constructor() {
        this.mainWindow = null;
        this.isDev = process.env.NODE_ENV === 'development';
        this.userDataPath = app.getPath('userData');
        this.dataPath = path.join(this.userDataPath, 'data');
        
        this.initializeApp();
    }

    initializeApp() {
        // Set app user model ID for Windows
        if (process.platform === 'win32') {
            app.setAppUserModelId('com.itevaluation.app');
        }

        // Handle app events
        app.whenReady().then(() => this.createMainWindow());
        app.on('window-all-closed', () => this.handleWindowAllClosed());
        app.on('activate', () => this.handleActivate());
        app.on('before-quit', () => this.handleBeforeQuit());

        // Handle IPC events
        this.setupIpcHandlers();

        // Create data directory
        this.ensureDataDirectory();
    }

    createMainWindow() {
        // Create the browser window
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1000,
            minHeight: 700,
            icon: this.getIconPath(),
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true,
                webSecurity: false
            },
            titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
            show: false // Don't show until ready
        });

        // Load the app
        const indexPath = path.join(__dirname, '..', 'index.html');
        this.mainWindow.loadFile(indexPath);

        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            if (this.isDev) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // Handle external links
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            shell.openExternal(url);
            return { action: 'deny' };
        });

        // Set up menu
        this.createMenu();

        // Handle window state
        this.handleWindowState();
    }

    getIconPath() {
        const iconName = process.platform === 'win32' ? 'icon.ico' : 
                        process.platform === 'darwin' ? 'icon.icns' : 'icon.png';
        return path.join(__dirname, '..', 'assets', iconName);
    }

    createMenu() {
        const template = [
            {
                label: 'ملف',
                submenu: [
                    {
                        label: 'نسخة احتياطية',
                        accelerator: 'CmdOrCtrl+B',
                        click: () => this.createBackup()
                    },
                    {
                        label: 'استيراد نسخة احتياطية',
                        click: () => this.importBackup()
                    },
                    { type: 'separator' },
                    {
                        label: 'تصدير البيانات',
                        submenu: [
                            {
                                label: 'تصدير المدارس',
                                click: () => this.exportData('schools')
                            },
                            {
                                label: 'تصدير الطلاب',
                                click: () => this.exportData('students')
                            },
                            {
                                label: 'تصدير الدرجات',
                                click: () => this.exportData('grades')
                            }
                        ]
                    },
                    { type: 'separator' },
                    {
                        label: 'إعدادات',
                        accelerator: 'CmdOrCtrl+,',
                        click: () => this.openSettings()
                    },
                    { type: 'separator' },
                    {
                        label: 'خروج',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => app.quit()
                    }
                ]
            },
            {
                label: 'تحرير',
                submenu: [
                    { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                    { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                    { type: 'separator' },
                    { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                    { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                    { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' },
                    { label: 'تحديد الكل', accelerator: 'CmdOrCtrl+A', role: 'selectall' }
                ]
            },
            {
                label: 'عرض',
                submenu: [
                    { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                    { label: 'إعادة تحميل قسري', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                    { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
                    { type: 'separator' },
                    { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
                    { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
                    { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
                    { type: 'separator' },
                    { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
                ]
            },
            {
                label: 'نافذة',
                submenu: [
                    { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                    { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
                ]
            },
            {
                label: 'مساعدة',
                submenu: [
                    {
                        label: 'حول التطبيق',
                        click: () => this.showAbout()
                    },
                    {
                        label: 'دليل المستخدم',
                        click: () => this.openUserGuide()
                    },
                    {
                        label: 'الإبلاغ عن مشكلة',
                        click: () => shell.openExternal('https://github.com/your-username/it-evaluation-system/issues')
                    }
                ]
            }
        ];

        // macOS specific menu adjustments
        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    { label: 'حول ' + app.getName(), role: 'about' },
                    { type: 'separator' },
                    { label: 'خدمات', role: 'services', submenu: [] },
                    { type: 'separator' },
                    { label: 'إخفاء ' + app.getName(), accelerator: 'Command+H', role: 'hide' },
                    { label: 'إخفاء الآخرين', accelerator: 'Command+Shift+H', role: 'hideothers' },
                    { label: 'إظهار الكل', role: 'unhide' },
                    { type: 'separator' },
                    { label: 'خروج', accelerator: 'Command+Q', click: () => app.quit() }
                ]
            });
        }

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    setupIpcHandlers() {
        // Handle backup creation
        ipcMain.handle('create-backup', async () => {
            return await this.createBackup();
        });

        // Handle data export
        ipcMain.handle('export-data', async (event, type) => {
            return await this.exportData(type);
        });

        // Handle file operations
        ipcMain.handle('show-save-dialog', async (event, options) => {
            const result = await dialog.showSaveDialog(this.mainWindow, options);
            return result;
        });

        ipcMain.handle('show-open-dialog', async (event, options) => {
            const result = await dialog.showOpenDialog(this.mainWindow, options);
            return result;
        });
    }

    ensureDataDirectory() {
        if (!fs.existsSync(this.dataPath)) {
            fs.mkdirSync(this.dataPath, { recursive: true });
        }
    }

    async createBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupFileName = `backup-${timestamp}.json`;
            
            const { filePath } = await dialog.showSaveDialog(this.mainWindow, {
                title: 'حفظ النسخة الاحتياطية',
                defaultPath: backupFileName,
                filters: [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (filePath) {
                // Read all data files
                const backupData = {
                    timestamp: new Date().toISOString(),
                    version: app.getVersion(),
                    data: {}
                };

                const dataFiles = ['schools.json', 'students.json', 'grades.json', 'settings.json'];
                
                for (const file of dataFiles) {
                    const filePath = path.join(this.dataPath, file);
                    if (fs.existsSync(filePath)) {
                        const content = fs.readFileSync(filePath, 'utf8');
                        backupData.data[file] = JSON.parse(content);
                    }
                }

                fs.writeFileSync(filePath, JSON.stringify(backupData, null, 2));
                
                dialog.showMessageBox(this.mainWindow, {
                    type: 'info',
                    title: 'نجح الحفظ',
                    message: 'تم إنشاء النسخة الاحتياطية بنجاح',
                    detail: `تم حفظ النسخة الاحتياطية في:\n${filePath}`
                });

                return filePath;
            }
        } catch (error) {
            console.error('Error creating backup:', error);
            dialog.showErrorBox('خطأ', 'فشل في إنشاء النسخة الاحتياطية');
        }
        return null;
    }

    async importBackup() {
        try {
            const { filePaths } = await dialog.showOpenDialog(this.mainWindow, {
                title: 'اختيار النسخة الاحتياطية',
                filters: [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ],
                properties: ['openFile']
            });

            if (filePaths && filePaths.length > 0) {
                const backupPath = filePaths[0];
                const backupContent = fs.readFileSync(backupPath, 'utf8');
                const backupData = JSON.parse(backupContent);

                // Restore data files
                if (backupData.data) {
                    for (const [fileName, content] of Object.entries(backupData.data)) {
                        const filePath = path.join(this.dataPath, fileName);
                        fs.writeFileSync(filePath, JSON.stringify(content, null, 2));
                    }
                }

                dialog.showMessageBox(this.mainWindow, {
                    type: 'info',
                    title: 'نجح الاستيراد',
                    message: 'تم استيراد النسخة الاحتياطية بنجاح',
                    detail: 'سيتم إعادة تحميل التطبيق لتطبيق التغييرات'
                });

                // Reload the app
                this.mainWindow.reload();
            }
        } catch (error) {
            console.error('Error importing backup:', error);
            dialog.showErrorBox('خطأ', 'فشل في استيراد النسخة الاحتياطية');
        }
    }

    async exportData(type) {
        // This would be implemented to export specific data types
        console.log(`Exporting ${type} data...`);
    }

    openSettings() {
        // Open settings window or navigate to settings section
        this.mainWindow.webContents.send('navigate-to-section', 'settings');
    }

    showAbout() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'حول التطبيق',
            message: 'استمارة تقويم تقنية المعلومات',
            detail: `الإصدار: ${app.getVersion()}\n\nنظام شامل لإدارة درجات الطلاب في مادة تقنية المعلومات\nللصفوف من الأول حتى الثاني عشر\n\nتم التطوير باستخدام Electron و JavaScript`
        });
    }

    openUserGuide() {
        // Open user guide or help documentation
        shell.openExternal('https://github.com/your-username/it-evaluation-system/wiki');
    }

    handleWindowState() {
        // Save and restore window state
        const windowStateFile = path.join(this.userDataPath, 'window-state.json');
        
        try {
            if (fs.existsSync(windowStateFile)) {
                const windowState = JSON.parse(fs.readFileSync(windowStateFile, 'utf8'));
                this.mainWindow.setBounds(windowState);
                
                if (windowState.isMaximized) {
                    this.mainWindow.maximize();
                }
            }
        } catch (error) {
            console.error('Error loading window state:', error);
        }

        // Save window state on close
        this.mainWindow.on('close', () => {
            try {
                const bounds = this.mainWindow.getBounds();
                const windowState = {
                    ...bounds,
                    isMaximized: this.mainWindow.isMaximized()
                };
                fs.writeFileSync(windowStateFile, JSON.stringify(windowState, null, 2));
            } catch (error) {
                console.error('Error saving window state:', error);
            }
        });
    }

    handleWindowAllClosed() {
        if (process.platform !== 'darwin') {
            app.quit();
        }
    }

    handleActivate() {
        if (BrowserWindow.getAllWindows().length === 0) {
            this.createMainWindow();
        }
    }

    handleBeforeQuit() {
        // Perform cleanup tasks
        console.log('Application is quitting...');
    }
}

// Create the application instance
new ITEvaluationApp();
