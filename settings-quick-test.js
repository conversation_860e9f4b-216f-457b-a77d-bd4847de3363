// Quick test for settings functionality
// Run this in browser console to test settings

console.log('🔧 Settings Quick Test');

async function testSettings() {
    console.log('📋 Testing Settings Manager...');
    
    try {
        // Test 1: Check if settings manager exists
        if (typeof window.settingsManager === 'undefined') {
            console.log('❌ Settings manager not found');
            return false;
        }
        
        console.log('✅ Settings manager found');
        
        // Test 2: Initialize settings manager
        if (!window.settingsManager.initialized) {
            console.log('🔄 Initializing settings manager...');
            await window.settingsManager.initialize();
        }
        
        console.log('✅ Settings manager initialized');
        
        // Test 3: Check current settings
        console.log('📊 Current settings:', window.settingsManager.settings);
        
        // Test 4: Test academic year functionality
        console.log('📅 Current academic year:', window.settingsManager.settings.currentAcademicYear);
        console.log('📅 Available academic years:', window.settingsManager.settings.academicYears);
        
        // Test 5: Test grade structure
        console.log('🎓 Grade structures:');
        Object.keys(window.settingsManager.settings.gradeSettings).forEach(range => {
            const gradeSettings = window.settingsManager.settings.gradeSettings[range];
            console.log(`  ${range}: Total=${gradeSettings.total}, Components=${Object.keys(gradeSettings.components).length}`);
        });
        
        // Test 6: Navigate to settings section
        console.log('🔄 Navigating to settings section...');
        const settingsSection = document.getElementById('settings');
        if (settingsSection) {
            // Simulate clicking settings nav button
            const settingsNavBtn = document.querySelector('[data-section="settings"]');
            if (settingsNavBtn) {
                settingsNavBtn.click();
                console.log('✅ Navigated to settings section');
                
                // Wait a bit for the interface to load
                setTimeout(() => {
                    const settingsContainer = document.querySelector('.settings-container');
                    if (settingsContainer && settingsContainer.innerHTML.trim() !== '') {
                        console.log('✅ Settings interface loaded successfully');
                        console.log('📊 Settings sections found:', settingsContainer.querySelectorAll('.settings-section').length);
                    } else {
                        console.log('❌ Settings interface not loaded');
                    }
                }, 1000);
            } else {
                console.log('❌ Settings navigation button not found');
            }
        } else {
            console.log('❌ Settings section not found');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Error testing settings:', error);
        return false;
    }
}

// Test academic year management
function testAcademicYearManagement() {
    console.log('📅 Testing Academic Year Management...');
    
    if (!window.settingsManager || !window.settingsManager.settings) {
        console.log('❌ Settings not available');
        return;
    }
    
    const settings = window.settingsManager.settings;
    
    console.log('Current academic year:', settings.currentAcademicYear);
    console.log('Available years:', settings.academicYears);
    console.log('Current semester:', settings.currentSemester);
    
    // Test validation
    const testYear = '2025-2026';
    console.log(`Testing if ${testYear} can be added...`);
    
    if (settings.academicYears.includes(testYear)) {
        console.log('❌ Year already exists');
    } else {
        console.log('✅ Year can be added');
    }
}

// Test grade structure validation
function testGradeStructureValidation() {
    console.log('🎓 Testing Grade Structure Validation...');
    
    if (!window.settingsManager || !window.settingsManager.settings) {
        console.log('❌ Settings not available');
        return;
    }
    
    const gradeSettings = window.settingsManager.settings.gradeSettings;
    
    Object.entries(gradeSettings).forEach(([range, settings]) => {
        const componentsSum = Object.values(settings.components)
            .reduce((sum, comp) => sum + comp.max, 0);
        
        const isValid = componentsSum === settings.total;
        const status = isValid ? '✅' : '❌';
        
        console.log(`${status} ${range}: Total=${settings.total}, Sum=${componentsSum}, Valid=${isValid}`);
        
        if (!isValid) {
            console.log(`  Components:`, Object.entries(settings.components).map(([key, comp]) => `${key}=${comp.max}`));
        }
    });
}

// Test settings save/load
async function testSettingsSaveLoad() {
    console.log('💾 Testing Settings Save/Load...');
    
    try {
        // Get current settings
        const originalSettings = await db.getSettings();
        console.log('📥 Loaded settings from database');
        
        // Create test settings
        const testSettings = {
            ...originalSettings,
            testField: 'test-value-' + Date.now()
        };
        
        // Save test settings
        await db.updateSettings(testSettings);
        console.log('💾 Saved test settings');
        
        // Load settings again
        const loadedSettings = await db.getSettings();
        
        if (loadedSettings.testField === testSettings.testField) {
            console.log('✅ Settings save/load working correctly');
            
            // Restore original settings
            delete loadedSettings.testField;
            await db.updateSettings(loadedSettings);
            console.log('🔄 Restored original settings');
        } else {
            console.log('❌ Settings save/load failed');
        }
        
    } catch (error) {
        console.error('❌ Error testing save/load:', error);
    }
}

// Test UI components
function testSettingsUI() {
    console.log('🎨 Testing Settings UI Components...');
    
    // Check for key UI elements
    const elements = [
        { selector: '#currentAcademicYear', name: 'Academic Year Select' },
        { selector: '#currentSemester', name: 'Semester Select' },
        { selector: '#saveSettingsBtn', name: 'Save Button' },
        { selector: '#resetSettingsBtn', name: 'Reset Button' },
        { selector: '.settings-section', name: 'Settings Sections' },
        { selector: '.tab-btn', name: 'Grade Structure Tabs' },
        { selector: '.form-input', name: 'Form Inputs' },
        { selector: '.checkbox-label', name: 'Checkboxes' }
    ];
    
    elements.forEach(({ selector, name }) => {
        const element = document.querySelector(selector);
        const status = element ? '✅' : '❌';
        const count = document.querySelectorAll(selector).length;
        console.log(`${status} ${name}: ${count} found`);
    });
}

// Run comprehensive test
async function runComprehensiveTest() {
    console.log('🚀 Running Comprehensive Settings Test...');
    console.log('='.repeat(50));
    
    await testSettings();
    
    setTimeout(() => {
        testAcademicYearManagement();
        testGradeStructureValidation();
        testSettingsUI();
    }, 2000);
    
    setTimeout(async () => {
        await testSettingsSaveLoad();
        console.log('='.repeat(50));
        console.log('✅ Comprehensive test completed!');
    }, 3000);
}

// Make functions available globally
window.testSettings = testSettings;
window.testAcademicYearManagement = testAcademicYearManagement;
window.testGradeStructureValidation = testGradeStructureValidation;
window.testSettingsSaveLoad = testSettingsSaveLoad;
window.testSettingsUI = testSettingsUI;
window.runComprehensiveTest = runComprehensiveTest;

// Auto-run basic test
console.log(`
🔧 Settings Test Commands Available:
- testSettings() - Basic settings test
- testAcademicYearManagement() - Test academic year features
- testGradeStructureValidation() - Test grade structure validation
- testSettingsSaveLoad() - Test save/load functionality
- testSettingsUI() - Test UI components
- runComprehensiveTest() - Run all tests

Quick start: runComprehensiveTest()
`);

// Auto-run if settings section is active
if (document.getElementById('settings')?.classList.contains('active')) {
    console.log('🔄 Settings section is active, running basic test...');
    testSettings();
}
