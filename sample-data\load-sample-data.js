// Script to load sample data into the application
// This can be run in the browser console or integrated into the app

async function loadSampleData() {
    try {
        console.log('Loading sample data...');
        
        // Load sample data files
        const sampleSchools = await fetch('./sample-data/sample-schools.json').then(r => r.json());
        const sampleStudents = await fetch('./sample-data/sample-students.json').then(r => r.json());
        const sampleGrades = await fetch('./sample-data/sample-grades.json').then(r => r.json());
        const sampleSettings = await fetch('./sample-data/sample-settings.json').then(r => r.json());
        
        // Clear existing data (optional)
        const clearExisting = confirm('هل تريد مسح البيانات الموجودة وتحميل البيانات التجريبية؟');
        
        if (clearExisting) {
            // Clear localStorage
            localStorage.removeItem('schools');
            localStorage.removeItem('students');
            localStorage.removeItem('grades');
            localStorage.removeItem('settings');
            
            console.log('Existing data cleared.');
        }
        
        // Load schools
        if (db && typeof db.addSchool === 'function') {
            for (const school of sampleSchools) {
                await db.addSchool(school);
            }
            console.log(`Loaded ${sampleSchools.length} schools`);
        } else {
            // Fallback to localStorage
            localStorage.setItem('schools', JSON.stringify(sampleSchools));
            console.log(`Loaded ${sampleSchools.length} schools to localStorage`);
        }
        
        // Load students
        if (db && typeof db.addStudents === 'function') {
            await db.addStudents(sampleStudents);
            console.log(`Loaded ${sampleStudents.length} students`);
        } else {
            // Fallback to localStorage
            localStorage.setItem('students', JSON.stringify(sampleStudents));
            console.log(`Loaded ${sampleStudents.length} students to localStorage`);
        }
        
        // Load grades
        if (db && typeof db.writeData === 'function') {
            await db.writeData('grades.json', sampleGrades);
            console.log(`Loaded ${sampleGrades.length} grade records`);
        } else {
            // Fallback to localStorage
            localStorage.setItem('grades', JSON.stringify(sampleGrades));
            console.log(`Loaded ${sampleGrades.length} grade records to localStorage`);
        }
        
        // Load settings
        if (db && typeof db.updateSettings === 'function') {
            await db.updateSettings(sampleSettings);
            console.log('Loaded settings');
        } else {
            // Fallback to localStorage
            localStorage.setItem('settings', JSON.stringify(sampleSettings));
            console.log('Loaded settings to localStorage');
        }
        
        // Refresh the application
        if (typeof app !== 'undefined' && app.refreshAllData) {
            await app.refreshAllData();
        } else {
            // Reload the page
            window.location.reload();
        }

        // Force recalculation of grades if grades manager is available
        setTimeout(() => {
            if (window.gradesManager && window.gradesManager.initialized) {
                // Trigger recalculation for all visible students
                const studentRows = document.querySelectorAll('.grades-table tbody tr[data-student-id]');
                studentRows.forEach(row => {
                    const studentId = row.dataset.studentId;
                    if (studentId && window.gradesManager.updateStudentCalculations) {
                        window.gradesManager.updateStudentCalculations(studentId);
                    }
                });
            }
        }, 1000);
        
        alert('تم تحميل البيانات التجريبية بنجاح!');
        console.log('Sample data loaded successfully!');
        
    } catch (error) {
        console.error('Error loading sample data:', error);
        alert('حدث خطأ أثناء تحميل البيانات التجريبية');
    }
}

// Function to create sample Excel template
function createSampleExcelTemplate() {
    const sampleData = [
        ['اسم الطالب', 'رقم الطالب', 'الصف الدراسي', 'الشعبة', 'تاريخ الميلاد', 'ملاحظات'],
        ['أحمد محمد علي', '12345', '5', 'أ', '2010-01-15', ''],
        ['فاطمة أحمد سالم', '12346', '5', 'أ', '2010-03-20', ''],
        ['محمد سعد الدين', '12347', '5', 'ب', '2010-02-10', ''],
        ['نورا عبدالله خالد', '12348', '5', 'ب', '2010-04-05', ''],
        ['عبدالرحمن يوسف', '12349', '5', 'ج', '2010-06-12', '']
    ];
    
    if (typeof XLSX !== 'undefined') {
        const ws = XLSX.utils.aoa_to_sheet(sampleData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'الطلاب');
        XLSX.writeFile(wb, 'نموذج_استيراد_الطلاب.xlsx');
        console.log('Sample Excel template created');
    } else {
        console.error('XLSX library not available');
    }
}

// Function to export current data as backup
async function exportCurrentData() {
    try {
        const backupData = {
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            schools: await db.getSchools() || JSON.parse(localStorage.getItem('schools') || '[]'),
            students: await db.getStudents() || JSON.parse(localStorage.getItem('students') || '[]'),
            grades: await db.getGrades() || JSON.parse(localStorage.getItem('grades') || '[]'),
            settings: await db.getSettings() || JSON.parse(localStorage.getItem('settings') || '{}')
        };
        
        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        console.log('Data exported successfully');
    } catch (error) {
        console.error('Error exporting data:', error);
    }
}

// Function to import data from backup file
function importDataFromFile(file) {
    const reader = new FileReader();
    reader.onload = async function(e) {
        try {
            const backupData = JSON.parse(e.target.result);
            
            if (backupData.schools) {
                localStorage.setItem('schools', JSON.stringify(backupData.schools));
            }
            if (backupData.students) {
                localStorage.setItem('students', JSON.stringify(backupData.students));
            }
            if (backupData.grades) {
                localStorage.setItem('grades', JSON.stringify(backupData.grades));
            }
            if (backupData.settings) {
                localStorage.setItem('settings', JSON.stringify(backupData.settings));
            }
            
            alert('تم استيراد البيانات بنجاح!');
            window.location.reload();
            
        } catch (error) {
            console.error('Error importing data:', error);
            alert('خطأ في استيراد البيانات');
        }
    };
    reader.readAsText(file);
}

// Add load sample data button to the page (for development)
function addSampleDataButton() {
    const button = document.createElement('button');
    button.textContent = 'تحميل البيانات التجريبية';
    button.className = 'btn btn-secondary';
    button.style.position = 'fixed';
    button.style.bottom = '20px';
    button.style.left = '20px';
    button.style.zIndex = '1000';
    button.onclick = loadSampleData;
    
    document.body.appendChild(button);
}

// Auto-add button in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', addSampleDataButton);
}

// Function to fix grade calculations
function fixGradeCalculations() {
    console.log('Fixing grade calculations...');

    // Check if we're on the grades page
    const gradesSection = document.getElementById('grades');
    if (!gradesSection || !gradesSection.classList.contains('active')) {
        console.log('Please navigate to the Grades section first');
        return;
    }

    // Get all student rows
    const studentRows = document.querySelectorAll('.grades-table tbody tr[data-student-id]');
    console.log(`Found ${studentRows.length} student rows`);

    studentRows.forEach((row, index) => {
        const studentId = row.dataset.studentId;
        if (studentId && window.gradesManager && window.gradesManager.updateStudentCalculations) {
            console.log(`Updating calculations for student ${index + 1}/${studentRows.length}`);
            window.gradesManager.updateStudentCalculations(studentId);
        }
    });

    console.log('Grade calculations fixed!');
}

// Make functions available globally
window.loadSampleData = loadSampleData;
window.createSampleExcelTemplate = createSampleExcelTemplate;
window.exportCurrentData = exportCurrentData;
window.importDataFromFile = importDataFromFile;
window.fixGradeCalculations = fixGradeCalculations;
