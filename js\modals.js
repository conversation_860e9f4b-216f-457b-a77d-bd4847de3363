// Modal management system

class ModalManager {
    constructor() {
        this.overlay = document.getElementById('modalOverlay');
        this.content = document.getElementById('modalContent');
        this.currentModal = null;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Close modal when clicking overlay
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.overlay.classList.contains('active')) {
                this.closeModal();
            }
        });
    }

    openModal(modalContent) {
        this.content.innerHTML = modalContent;
        this.overlay.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Add close button functionality
        const closeBtn = this.content.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.closeModal());
        }
    }

    closeModal() {
        this.overlay.classList.remove('active');
        document.body.style.overflow = '';
        this.content.innerHTML = '';
        this.currentModal = null;
    }

    // School modals
    openAddSchoolModal() {
        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">إضافة مدرسة جديدة</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addSchoolForm">
                <div class="form-group">
                    <label class="form-label">اسم المدرسة</label>
                    <input type="text" id="schoolName" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">رمز المدرسة</label>
                    <input type="text" id="schoolCode" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">العنوان</label>
                    <input type="text" id="schoolAddress" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label">رقم الهاتف</label>
                    <input type="tel" id="schoolPhone" class="form-input">
                </div>
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" id="schoolEmail" class="form-input">
                </div>

                <!-- Staff Information Section -->
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-users"></i> بيانات الكادر التعليمي والإداري
                    </h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">اسم المعلم</label>
                            <input type="text" id="teacherName" class="form-input" placeholder="اسم معلم تقنية المعلومات">
                        </div>
                        <div class="form-group">
                            <label class="form-label">اسم المشرف</label>
                            <input type="text" id="supervisorName" class="form-input" placeholder="اسم المشرف التربوي">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">اسم مدير المدرسة</label>
                        <input type="text" id="principalName" class="form-input" placeholder="اسم مدير/مديرة المدرسة">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea id="schoolNotes" class="form-textarea" placeholder="ملاحظات إضافية عن المدرسة"></textarea>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        `;

        this.openModal(modalContent);

        // Handle form submission
        document.getElementById('addSchoolForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleAddSchool();
        });
    }

    openEditSchoolModal(school) {
        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">تعديل بيانات المدرسة</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editSchoolForm">
                <input type="hidden" id="schoolId" value="${school.id}">
                <div class="form-group">
                    <label class="form-label">اسم المدرسة</label>
                    <input type="text" id="schoolName" class="form-input" value="${school.name}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">رمز المدرسة</label>
                    <input type="text" id="schoolCode" class="form-input" value="${school.code}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">العنوان</label>
                    <input type="text" id="schoolAddress" class="form-input" value="${school.address || ''}">
                </div>
                <div class="form-group">
                    <label class="form-label">رقم الهاتف</label>
                    <input type="tel" id="schoolPhone" class="form-input" value="${school.phone || ''}">
                </div>
                <div class="form-group">
                    <label class="form-label">البريد الإلكتروني</label>
                    <input type="email" id="schoolEmail" class="form-input" value="${school.email || ''}">
                </div>

                <!-- Staff Information Section -->
                <div class="form-section">
                    <h4 class="form-section-title">
                        <i class="fas fa-users"></i> بيانات الكادر التعليمي والإداري
                    </h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">اسم المعلم</label>
                            <input type="text" id="teacherName" class="form-input" value="${school.teacherName || ''}" placeholder="اسم معلم تقنية المعلومات">
                        </div>
                        <div class="form-group">
                            <label class="form-label">اسم المشرف</label>
                            <input type="text" id="supervisorName" class="form-input" value="${school.supervisorName || ''}" placeholder="اسم المشرف التربوي">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">اسم مدير المدرسة</label>
                        <input type="text" id="principalName" class="form-input" value="${school.principalName || ''}" placeholder="اسم مدير/مديرة المدرسة">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea id="schoolNotes" class="form-textarea" placeholder="ملاحظات إضافية عن المدرسة">${school.notes || ''}</textarea>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        `;

        this.openModal(modalContent);

        // Handle form submission
        document.getElementById('editSchoolForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleEditSchool();
        });
    }

    // Student modals
    async openAddStudentModal() {
        // Generate automatic student ID
        const nextStudentId = await this.generateNextStudentId();

        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">إضافة طالب جديد</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addStudentForm">
                <div class="form-group">
                    <label class="form-label">اسم الطالب</label>
                    <input type="text" id="studentName" class="form-input" required placeholder="أدخل اسم الطالب الكامل">
                </div>
                <div class="form-group">
                    <label class="form-label">رقم الطالب</label>
                    <div class="student-id-container">
                        <input type="text" id="studentId" class="form-input" value="${nextStudentId}" readonly>
                        <button type="button" class="btn-icon refresh-student-id" title="إنشاء رقم جديد">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <small class="form-help">رقم الطالب يتم إنشاؤه تلقائياً</small>
                </div>
                <div class="form-group">
                    <label class="form-label">المدرسة</label>
                    <select id="studentSchool" class="form-select" required>
                        <option value="">اختر المدرسة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">الصف الدراسي</label>
                    <select id="studentGrade" class="form-select" required>
                        <option value="">اختر الصف</option>
                        ${this.generateGradeOptions()}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">الشعبة</label>
                    <input type="text" id="studentSection" class="form-input" required placeholder="مثال: أ، ب، ج">
                </div>
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea id="studentNotes" class="form-textarea" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        `;

        this.openModal(modalContent);
        this.loadSchoolsForSelect('studentSchool');
        this.setupStudentIdRefresh();

        // Handle form submission
        document.getElementById('addStudentForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleAddStudent();
        });
    }

    async generateNextStudentId() {
        try {
            // Get all existing students
            const students = await db.getStudents();

            // Get current academic year
            const currentYear = window.app?.settings?.currentAcademicYear || '2024-2025';
            const yearSuffix = currentYear.split('-')[0].slice(-2); // Get last 2 digits of first year

            // Find the highest existing student ID for this year
            let maxNumber = 0;
            const yearPattern = new RegExp(`^${yearSuffix}(\\d{4})$`);

            students.forEach(student => {
                const match = student.studentId.match(yearPattern);
                if (match) {
                    const number = parseInt(match[1]);
                    if (number > maxNumber) {
                        maxNumber = number;
                    }
                }
            });

            // Generate next number
            const nextNumber = maxNumber + 1;
            const paddedNumber = nextNumber.toString().padStart(4, '0');

            return `${yearSuffix}${paddedNumber}`;

        } catch (error) {
            console.error('Error generating student ID:', error);
            // Fallback to timestamp-based ID
            const timestamp = Date.now().toString().slice(-6);
            return `24${timestamp}`;
        }
    }

    setupStudentIdRefresh() {
        const refreshBtn = document.querySelector('.refresh-student-id');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                const studentIdInput = document.getElementById('studentId');
                if (studentIdInput) {
                    // Show loading state
                    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    refreshBtn.disabled = true;

                    try {
                        const newId = await this.generateNextStudentId();
                        studentIdInput.value = newId;
                        Utils.showNotification('تم إنشاء رقم طالب جديد', 'success');
                    } catch (error) {
                        Utils.showNotification('خطأ في إنشاء رقم الطالب', 'error');
                    } finally {
                        // Restore button state
                        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
                        refreshBtn.disabled = false;
                    }
                }
            });
        }
    }

    openEditStudentModal(student) {
        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">تعديل بيانات الطالب</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editStudentForm">
                <input type="hidden" id="studentIdHidden" value="${student.id}">
                <div class="form-group">
                    <label class="form-label">اسم الطالب</label>
                    <input type="text" id="studentName" class="form-input" value="${student.name}" required>
                </div>
                <div class="form-group">
                    <label class="form-label">رقم الطالب</label>
                    <input type="text" id="studentId" class="form-input" value="${student.studentId}" readonly>
                    <small class="form-help">لا يمكن تعديل رقم الطالب بعد الإنشاء</small>
                </div>
                <div class="form-group">
                    <label class="form-label">المدرسة</label>
                    <select id="studentSchool" class="form-select" required>
                        <option value="">اختر المدرسة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">الصف الدراسي</label>
                    <select id="studentGrade" class="form-select" required>
                        <option value="">اختر الصف</option>
                        ${this.generateGradeOptions()}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">الشعبة</label>
                    <input type="text" id="studentSection" class="form-input" value="${student.section}" required placeholder="مثال: أ، ب، ج">
                </div>
                <div class="form-group">
                    <label class="form-label">ملاحظات</label>
                    <textarea id="studentNotes" class="form-textarea" placeholder="ملاحظات إضافية (اختياري)">${student.notes || ''}</textarea>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        `;

        this.openModal(modalContent);
        this.loadSchoolsForSelect('studentSchool', student.schoolId);
        document.getElementById('studentGrade').value = student.grade;

        // Handle form submission
        document.getElementById('editStudentForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleEditStudent();
        });
    }

    openImportStudentsModal() {
        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">استيراد الطلاب من Excel</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">اختر المدرسة</label>
                    <select id="importSchool" class="form-select" required>
                        <option value="">اختر المدرسة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">ملف Excel</label>
                    <input type="file" id="excelFile" class="form-input" accept=".xlsx,.xls" required>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-secondary" onclick="modalManager.downloadTemplate()">
                        <i class="fas fa-download"></i> تحميل نموذج Excel
                    </button>
                </div>
                <div id="importPreview" class="d-none">
                    <h4>معاينة البيانات:</h4>
                    <div id="previewTable"></div>
                </div>
                <div class="modal-actions">
                    <button type="button" id="importBtn" class="btn btn-primary d-none">
                        <i class="fas fa-upload"></i> استيراد البيانات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </div>
        `;

        this.openModal(modalContent);
        this.loadSchoolsForSelect('importSchool');

        // Handle file selection
        document.getElementById('excelFile').addEventListener('change', (e) => {
            this.handleFileSelection(e);
        });

        // Handle import button
        document.getElementById('importBtn').addEventListener('click', () => {
            this.handleImportStudents();
        });
    }

    // Utility methods
    generateGradeOptions() {
        let options = '';
        for (let i = 1; i <= 12; i++) {
            options += `<option value="${i}">الصف ${this.getGradeName(i)}</option>`;
        }
        return options;
    }

    getGradeName(grade) {
        const names = {
            1: 'الأول', 2: 'الثاني', 3: 'الثالث', 4: 'الرابع',
            5: 'الخامس', 6: 'السادس', 7: 'السابع', 8: 'الثامن',
            9: 'التاسع', 10: 'العاشر', 11: 'الحادي عشر', 12: 'الثاني عشر'
        };
        return names[grade] || grade.toString();
    }

    async loadSchoolsForSelect(selectId, selectedValue = '') {
        const select = document.getElementById(selectId);
        if (!select) return;

        try {
            const schools = await db.getSchools();
            select.innerHTML = '<option value="">اختر المدرسة</option>';
            
            schools.forEach(school => {
                const option = document.createElement('option');
                option.value = school.id;
                option.textContent = school.name;
                if (school.id === selectedValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading schools:', error);
            Utils.showNotification('خطأ في تحميل المدارس', 'error');
        }
    }

    // Event handlers
    async handleAddSchool() {
        try {
            const schoolData = {
                name: document.getElementById('schoolName').value.trim(),
                code: document.getElementById('schoolCode').value.trim(),
                address: document.getElementById('schoolAddress').value.trim(),
                phone: document.getElementById('schoolPhone').value.trim(),
                email: document.getElementById('schoolEmail').value.trim(),
                teacherName: document.getElementById('teacherName').value.trim(),
                supervisorName: document.getElementById('supervisorName').value.trim(),
                principalName: document.getElementById('principalName').value.trim(),
                notes: document.getElementById('schoolNotes').value.trim()
            };

            const errors = Utils.validateSchool(schoolData);
            if (errors.length > 0) {
                Utils.showNotification(errors.join('<br>'), 'error');
                return;
            }

            await db.addSchool(schoolData);
            Utils.showNotification('تم إضافة المدرسة بنجاح', 'success');
            this.closeModal();

            // Refresh schools list if on schools page
            if (window.schoolsManager) {
                await window.schoolsManager.loadSchools();
            }
        } catch (error) {
            console.error('Error adding school:', error);
            Utils.showNotification('خطأ في إضافة المدرسة', 'error');
        }
    }

    async handleEditSchool() {
        try {
            const schoolId = document.getElementById('schoolId').value;
            const schoolData = {
                name: document.getElementById('schoolName').value.trim(),
                code: document.getElementById('schoolCode').value.trim(),
                address: document.getElementById('schoolAddress').value.trim(),
                phone: document.getElementById('schoolPhone').value.trim(),
                email: document.getElementById('schoolEmail').value.trim(),
                teacherName: document.getElementById('teacherName').value.trim(),
                supervisorName: document.getElementById('supervisorName').value.trim(),
                principalName: document.getElementById('principalName').value.trim(),
                notes: document.getElementById('schoolNotes').value.trim()
            };

            const errors = Utils.validateSchool(schoolData);
            if (errors.length > 0) {
                Utils.showNotification(errors.join('<br>'), 'error');
                return;
            }

            await db.updateSchool(schoolId, schoolData);
            Utils.showNotification('تم تحديث بيانات المدرسة بنجاح', 'success');
            this.closeModal();

            // Refresh schools list if on schools page
            if (window.schoolsManager) {
                await window.schoolsManager.loadSchools();
            }
        } catch (error) {
            console.error('Error updating school:', error);
            Utils.showNotification('خطأ في تحديث بيانات المدرسة', 'error');
        }
    }

    async handleAddStudent() {
        try {
            const studentData = {
                name: document.getElementById('studentName').value.trim(),
                studentId: document.getElementById('studentId').value.trim(),
                schoolId: document.getElementById('studentSchool').value,
                grade: parseInt(document.getElementById('studentGrade').value),
                section: document.getElementById('studentSection').value.trim(),
                notes: document.getElementById('studentNotes').value.trim()
            };

            // Validate required fields
            if (!studentData.name) {
                Utils.showNotification('يرجى إدخال اسم الطالب', 'error');
                return;
            }

            if (!studentData.studentId) {
                Utils.showNotification('رقم الطالب مطلوب', 'error');
                return;
            }

            if (!studentData.schoolId) {
                Utils.showNotification('يرجى اختيار المدرسة', 'error');
                return;
            }

            if (!studentData.grade) {
                Utils.showNotification('يرجى اختيار الصف الدراسي', 'error');
                return;
            }

            if (!studentData.section) {
                Utils.showNotification('يرجى إدخال الشعبة', 'error');
                return;
            }

            // Check if student ID already exists
            const existingStudents = await db.getStudents();
            const duplicateId = existingStudents.find(s => s.studentId === studentData.studentId);
            if (duplicateId) {
                Utils.showNotification('رقم الطالب موجود بالفعل، يرجى إنشاء رقم جديد', 'error');
                return;
            }

            await db.addStudent(studentData);
            Utils.showNotification('تم إضافة الطالب بنجاح', 'success');
            this.closeModal();

            // Refresh students list if on students page
            if (window.studentsManager) {
                await window.studentsManager.loadStudents();
            }
        } catch (error) {
            console.error('Error adding student:', error);
            Utils.showNotification('خطأ في إضافة الطالب', 'error');
        }
    }

    async handleEditStudent() {
        try {
            const studentId = document.getElementById('studentIdHidden').value;
            const studentData = {
                name: document.getElementById('studentName').value.trim(),
                studentId: document.getElementById('studentId').value.trim(),
                schoolId: document.getElementById('studentSchool').value,
                grade: parseInt(document.getElementById('studentGrade').value),
                section: document.getElementById('studentSection').value.trim(),
                notes: document.getElementById('studentNotes').value.trim()
            };

            // Validate required fields
            if (!studentData.name) {
                Utils.showNotification('يرجى إدخال اسم الطالب', 'error');
                return;
            }

            if (!studentData.schoolId) {
                Utils.showNotification('يرجى اختيار المدرسة', 'error');
                return;
            }

            if (!studentData.grade) {
                Utils.showNotification('يرجى اختيار الصف الدراسي', 'error');
                return;
            }

            if (!studentData.section) {
                Utils.showNotification('يرجى إدخال الشعبة', 'error');
                return;
            }

            await db.updateStudent(studentId, studentData);
            Utils.showNotification('تم تحديث بيانات الطالب بنجاح', 'success');
            this.closeModal();

            // Refresh students list if on students page
            if (window.studentsManager) {
                await window.studentsManager.loadStudents();
            }
        } catch (error) {
            console.error('Error updating student:', error);
            Utils.showNotification('خطأ في تحديث بيانات الطالب', 'error');
        }
    }

    downloadTemplate() {
        const template = Utils.generateStudentTemplate();
        const data = [template.headers, ...template.data];
        Utils.exportToExcel(data, 'نموذج_استيراد_الطلاب.xlsx');
    }

    handleFileSelection(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, { type: 'array' });
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                this.previewImportData(jsonData);
            } catch (error) {
                console.error('Error reading Excel file:', error);
                Utils.showNotification('خطأ في قراءة ملف Excel', 'error');
            }
        };
        reader.readAsArrayBuffer(file);
    }

    previewImportData(data) {
        const { students, errors } = Utils.parseStudentExcel(data);
        
        const previewDiv = document.getElementById('importPreview');
        const previewTable = document.getElementById('previewTable');
        const importBtn = document.getElementById('importBtn');

        if (errors.length > 0) {
            previewTable.innerHTML = `
                <div class="alert alert-error">
                    <h4>أخطاء في البيانات:</h4>
                    <ul>
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `;
            importBtn.classList.add('d-none');
        } else {
            previewTable.innerHTML = `
                <div class="alert alert-success">
                    <p>تم العثور على ${students.length} طالب صالح للاستيراد</p>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>اسم الطالب</th>
                            <th>رقم الطالب</th>
                            <th>الصف</th>
                            <th>الشعبة</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${students.slice(0, 5).map(student => `
                            <tr>
                                <td>${student.name}</td>
                                <td>${student.studentId}</td>
                                <td>${student.grade}</td>
                                <td>${student.section}</td>
                            </tr>
                        `).join('')}
                        ${students.length > 5 ? `<tr><td colspan="4">... و ${students.length - 5} طالب آخر</td></tr>` : ''}
                    </tbody>
                </table>
            `;
            importBtn.classList.remove('d-none');
            this.studentsToImport = students;
        }

        previewDiv.classList.remove('d-none');
    }

    async handleImportStudents() {
        if (!this.studentsToImport || this.studentsToImport.length === 0) {
            Utils.showNotification('لا توجد بيانات للاستيراد', 'error');
            return;
        }

        const schoolId = document.getElementById('importSchool').value;
        if (!schoolId) {
            Utils.showNotification('يجب اختيار المدرسة', 'error');
            return;
        }

        try {
            // Add school ID to all students
            const studentsWithSchool = this.studentsToImport.map(student => ({
                ...student,
                schoolId: schoolId
            }));

            await db.addStudents(studentsWithSchool);
            Utils.showNotification(`تم استيراد ${studentsWithSchool.length} طالب بنجاح`, 'success');
            this.closeModal();
            
            // Refresh students list if on students page
            if (window.studentsManager) {
                await window.studentsManager.loadStudents();
            }
        } catch (error) {
            console.error('Error importing students:', error);
            Utils.showNotification('خطأ في استيراد الطلاب', 'error');
        }
    }
}

// Create global modal manager instance
const modalManager = new ModalManager();
