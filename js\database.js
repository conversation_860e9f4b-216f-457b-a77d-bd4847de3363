// Database Management System using Node.js fs, path, and os modules
// This will work when the app is converted to Electron

class Database {
    constructor() {
        this.isElectron = this.checkElectronEnvironment();
        this.dataPath = this.getDataPath();
        this.initializeDatabase();
    }

    // Check if running in Electron environment
    checkElectronEnvironment() {
        return typeof window !== 'undefined' && window.process && window.process.type;
    }

    // Get the appropriate data path based on environment
    getDataPath() {
        if (this.isElectron) {
            const { app } = require('electron').remote || require('@electron/remote');
            const path = require('path');
            const os = require('os');
            
            // Use app data directory for persistent storage
            const userDataPath = app.getPath('userData');
            return path.join(userDataPath, 'data');
        } else {
            // Fallback to localStorage for web environment
            return null;
        }
    }

    // Initialize database structure
    async initializeDatabase() {
        if (this.isElectron) {
            const fs = require('fs').promises;
            const path = require('path');

            try {
                // Create data directory if it doesn't exist
                await fs.mkdir(this.dataPath, { recursive: true });

                // Initialize data files if they don't exist
                const dataFiles = [
                    'schools.json',
                    'students.json',
                    'grades.json',
                    'settings.json'
                ];

                for (const file of dataFiles) {
                    const filePath = path.join(this.dataPath, file);
                    try {
                        await fs.access(filePath);
                    } catch (error) {
                        // File doesn't exist, create it with empty structure
                        const initialData = this.getInitialData(file);
                        await fs.writeFile(filePath, JSON.stringify(initialData, null, 2));
                    }
                }
            } catch (error) {
                console.error('Error initializing database:', error);
            }
        } else {
            // Initialize localStorage structure for web environment
            this.initializeLocalStorage();
        }
    }

    // Get initial data structure for each file
    getInitialData(filename) {
        const initialStructures = {
            'schools.json': [],
            'students.json': [],
            'grades.json': [],
            'settings.json': {
                currentAcademicYear: '2024-2025',
                currentSemester: 1,
                appVersion: '1.0.0',
                lastBackup: null
            }
        };
        return initialStructures[filename] || {};
    }

    // Initialize localStorage for web environment
    initializeLocalStorage() {
        const keys = ['schools', 'students', 'grades', 'settings'];
        keys.forEach(key => {
            if (!localStorage.getItem(key)) {
                const initialData = this.getInitialData(`${key}.json`);
                localStorage.setItem(key, JSON.stringify(initialData));
            }
        });
    }

    // Read data from file or localStorage
    async readData(filename) {
        if (this.isElectron) {
            const fs = require('fs').promises;
            const path = require('path');
            
            try {
                const filePath = path.join(this.dataPath, filename);
                const data = await fs.readFile(filePath, 'utf8');
                return JSON.parse(data);
            } catch (error) {
                console.error(`Error reading ${filename}:`, error);
                return this.getInitialData(filename);
            }
        } else {
            // Use localStorage
            const key = filename.replace('.json', '');
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : this.getInitialData(filename);
        }
    }

    // Write data to file or localStorage
    async writeData(filename, data) {
        if (this.isElectron) {
            const fs = require('fs').promises;
            const path = require('path');
            
            try {
                const filePath = path.join(this.dataPath, filename);
                await fs.writeFile(filePath, JSON.stringify(data, null, 2));
                return true;
            } catch (error) {
                console.error(`Error writing ${filename}:`, error);
                return false;
            }
        } else {
            // Use localStorage
            try {
                const key = filename.replace('.json', '');
                localStorage.setItem(key, JSON.stringify(data));
                return true;
            } catch (error) {
                console.error(`Error writing to localStorage:`, error);
                return false;
            }
        }
    }

    // Schools CRUD operations
    async getSchools() {
        return await this.readData('schools.json');
    }

    async addSchool(school) {
        const schools = await this.getSchools();
        school.id = this.generateId();
        school.createdAt = new Date().toISOString();
        schools.push(school);
        await this.writeData('schools.json', schools);
        return school;
    }

    async updateSchool(id, updatedSchool) {
        const schools = await this.getSchools();
        const index = schools.findIndex(school => school.id === id);
        if (index !== -1) {
            schools[index] = { ...schools[index], ...updatedSchool, updatedAt: new Date().toISOString() };
            await this.writeData('schools.json', schools);
            return schools[index];
        }
        return null;
    }

    async deleteSchool(id) {
        const schools = await this.getSchools();
        const filteredSchools = schools.filter(school => school.id !== id);
        await this.writeData('schools.json', filteredSchools);
        return filteredSchools.length < schools.length;
    }

    // Students CRUD operations
    async getStudents() {
        return await this.readData('students.json');
    }

    async addStudent(student) {
        const students = await this.getStudents();
        student.id = this.generateId();
        student.createdAt = new Date().toISOString();
        students.push(student);
        await this.writeData('students.json', students);
        return student;
    }

    async addStudents(studentsArray) {
        const students = await this.getStudents();
        const newStudents = studentsArray.map(student => ({
            ...student,
            id: this.generateId(),
            createdAt: new Date().toISOString()
        }));
        students.push(...newStudents);
        await this.writeData('students.json', students);
        return newStudents;
    }

    async updateStudent(id, updatedStudent) {
        const students = await this.getStudents();
        const index = students.findIndex(student => student.id === id);
        if (index !== -1) {
            students[index] = { ...students[index], ...updatedStudent, updatedAt: new Date().toISOString() };
            await this.writeData('students.json', students);
            return students[index];
        }
        return null;
    }

    async deleteStudent(id) {
        const students = await this.getStudents();
        const filteredStudents = students.filter(student => student.id !== id);
        await this.writeData('students.json', filteredStudents);
        return filteredStudents.length < students.length;
    }

    // Grades CRUD operations
    async getGrades() {
        return await this.readData('grades.json');
    }

    async addGrade(grade) {
        const grades = await this.getGrades();
        grade.id = this.generateId();
        grade.createdAt = new Date().toISOString();
        grades.push(grade);
        await this.writeData('grades.json', grades);
        return grade;
    }

    async updateGrade(id, updatedGrade) {
        const grades = await this.getGrades();
        const index = grades.findIndex(grade => grade.id === id);
        if (index !== -1) {
            grades[index] = { ...grades[index], ...updatedGrade, updatedAt: new Date().toISOString() };
            await this.writeData('grades.json', grades);
            return grades[index];
        }
        return null;
    }

    async saveStudentGrades(studentId, schoolId, grade, section, semester, academicYear, gradesData) {
        const grades = await this.getGrades();
        
        // Find existing grade record or create new one
        let gradeRecord = grades.find(g => 
            g.studentId === studentId && 
            g.schoolId === schoolId && 
            g.grade === grade && 
            g.section === section && 
            g.semester === semester && 
            g.academicYear === academicYear
        );

        if (gradeRecord) {
            // Update existing record
            gradeRecord.grades = gradesData;
            gradeRecord.updatedAt = new Date().toISOString();
        } else {
            // Create new record
            gradeRecord = {
                id: this.generateId(),
                studentId,
                schoolId,
                grade,
                section,
                semester,
                academicYear,
                grades: gradesData,
                createdAt: new Date().toISOString()
            };
            grades.push(gradeRecord);
        }

        await this.writeData('grades.json', grades);
        return gradeRecord;
    }

    // Settings operations
    async getSettings() {
        return await this.readData('settings.json');
    }

    async updateSettings(newSettings) {
        const settings = await this.getSettings();
        const updatedSettings = { ...settings, ...newSettings };
        await this.writeData('settings.json', updatedSettings);
        return updatedSettings;
    }

    // Utility functions
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // Backup and restore functions
    async createBackup() {
        if (this.isElectron) {
            const fs = require('fs').promises;
            const path = require('path');
            
            try {
                const backupData = {
                    schools: await this.getSchools(),
                    students: await this.getStudents(),
                    grades: await this.getGrades(),
                    settings: await this.getSettings(),
                    backupDate: new Date().toISOString()
                };

                const backupPath = path.join(this.dataPath, 'backups');
                await fs.mkdir(backupPath, { recursive: true });
                
                const backupFilename = `backup_${new Date().toISOString().split('T')[0]}_${Date.now()}.json`;
                const backupFilePath = path.join(backupPath, backupFilename);
                
                await fs.writeFile(backupFilePath, JSON.stringify(backupData, null, 2));
                
                // Update last backup date in settings
                await this.updateSettings({ lastBackup: new Date().toISOString() });
                
                return backupFilePath;
            } catch (error) {
                console.error('Error creating backup:', error);
                return null;
            }
        }
        return null;
    }

    // Search and filter functions
    async searchStudents(query, filters = {}) {
        const students = await this.getStudents();
        let filteredStudents = students;

        // Apply text search
        if (query) {
            filteredStudents = filteredStudents.filter(student =>
                student.name.toLowerCase().includes(query.toLowerCase()) ||
                student.studentId.toLowerCase().includes(query.toLowerCase())
            );
        }

        // Apply filters
        if (filters.schoolId) {
            filteredStudents = filteredStudents.filter(student => student.schoolId === filters.schoolId);
        }
        if (filters.grade) {
            filteredStudents = filteredStudents.filter(student => student.grade === parseInt(filters.grade));
        }
        if (filters.section) {
            filteredStudents = filteredStudents.filter(student => student.section === filters.section);
        }

        return filteredStudents;
    }

    async getStudentGrades(studentId, academicYear, semester) {
        const grades = await this.getGrades();
        return grades.filter(grade =>
            grade.studentId === studentId &&
            grade.academicYear === academicYear &&
            grade.semester === semester
        );
    }

    // Settings management
    async getSettings() {
        try {
            if (this.isElectron) {
                const settingsPath = this.path.join(this.userDataPath, 'settings.json');
                if (await this.fileExists(settingsPath)) {
                    const data = await this.fs.readFile(settingsPath, 'utf8');
                    return JSON.parse(data);
                }
            } else {
                const data = localStorage.getItem('settings');
                if (data) {
                    return JSON.parse(data);
                }
            }
            return null;
        } catch (error) {
            console.error('Error loading settings:', error);
            return null;
        }
    }

    async updateSettings(settings) {
        try {
            settings.updatedAt = new Date().toISOString();

            if (this.isElectron) {
                const settingsPath = this.path.join(this.userDataPath, 'settings.json');
                await this.ensureDirectoryExists(this.path.dirname(settingsPath));
                await this.fs.writeFile(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
            } else {
                localStorage.setItem('settings', JSON.stringify(settings));
            }

            return true;
        } catch (error) {
            console.error('Error saving settings:', error);
            throw error;
        }
    }

    // Backup and restore functions
    async createBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupData = {
                timestamp: timestamp,
                version: '1.0.0',
                schools: await this.getSchools(),
                students: await this.getStudents(),
                grades: await this.getGrades(),
                settings: await this.getSettings(),
                approvals: await this.getApprovals()
            };

            const backupJson = JSON.stringify(backupData, null, 2);

            if (this.isElectron) {
                // Save to file in Electron
                const backupPath = this.path.join(this.userDataPath, 'backups', `backup-${timestamp}.json`);
                await this.ensureDirectoryExists(this.path.dirname(backupPath));
                await this.fs.writeFile(backupPath, backupJson, 'utf8');
                return backupPath;
            } else {
                // Download in browser
                const blob = new Blob([backupJson], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `backup-${timestamp}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                return `backup-${timestamp}.json`;
            }
        } catch (error) {
            console.error('Error creating backup:', error);
            throw error;
        }
    }

    async restoreFromBackup(backupData) {
        try {
            // Validate backup data
            if (!backupData.schools || !backupData.students || !backupData.grades) {
                throw new Error('Invalid backup data format');
            }

            // Restore data
            await this.saveSchools(backupData.schools);
            await this.saveStudents(backupData.students);
            await this.saveGrades(backupData.grades);

            if (backupData.settings) {
                await this.updateSettings(backupData.settings);
            }

            if (backupData.approvals) {
                await this.saveApprovals(backupData.approvals);
            }

            return true;
        } catch (error) {
            console.error('Error restoring backup:', error);
            throw error;
        }
    }

    // Approvals management
    async getApprovals() {
        try {
            if (this.isElectron) {
                const approvalsPath = this.path.join(this.userDataPath, 'approvals.json');
                if (await this.fileExists(approvalsPath)) {
                    const data = await this.fs.readFile(approvalsPath, 'utf8');
                    return JSON.parse(data);
                }
            } else {
                const data = localStorage.getItem('approvals');
                if (data) {
                    return JSON.parse(data);
                }
            }
            return [];
        } catch (error) {
            console.error('Error loading approvals:', error);
            return [];
        }
    }

    async saveApprovals(approvals) {
        try {
            if (this.isElectron) {
                const approvalsPath = this.path.join(this.userDataPath, 'approvals.json');
                await this.ensureDirectoryExists(this.path.dirname(approvalsPath));
                await this.fs.writeFile(approvalsPath, JSON.stringify(approvals, null, 2), 'utf8');
            } else {
                localStorage.setItem('approvals', JSON.stringify(approvals));
            }
            return true;
        } catch (error) {
            console.error('Error saving approvals:', error);
            throw error;
        }
    }

    async addApproval(approval) {
        try {
            const approvals = await this.getApprovals();
            approval.id = approval.id || Utils.generateId();
            approval.createdAt = new Date().toISOString();

            approvals.push(approval);
            await this.saveApprovals(approvals);
            return approval;
        } catch (error) {
            console.error('Error adding approval:', error);
            throw error;
        }
    }

    async removeApproval(studentId, academicYear) {
        try {
            const approvals = await this.getApprovals();
            const filteredApprovals = approvals.filter(approval =>
                !(approval.studentId === studentId && approval.academicYear === academicYear)
            );

            await this.saveApprovals(filteredApprovals);
            return true;
        } catch (error) {
            console.error('Error removing approval:', error);
            throw error;
        }
    }

    async getStudentApproval(studentId, academicYear) {
        try {
            const approvals = await this.getApprovals();
            return approvals.find(approval =>
                approval.studentId === studentId && approval.academicYear === academicYear
            );
        } catch (error) {
            console.error('Error getting student approval:', error);
            return null;
        }
    }

    async getApprovalsByYear(academicYear) {
        try {
            const approvals = await this.getApprovals();
            return approvals.filter(approval => approval.academicYear === academicYear);
        } catch (error) {
            console.error('Error getting approvals by year:', error);
            return [];
        }
    }
}

// Create global database instance
const db = new Database();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Database;
}
