// Settings management system

class SettingsManager {
    constructor() {
        this.settings = {};
        this.defaultSettings = {
            currentAcademicYear: '2024-2025',
            currentSemester: 1,
            appVersion: '1.0.0',
            language: 'ar',
            theme: 'light',
            dateFormat: 'DD/MM/YYYY',
            numberFormat: 'ar-SA',
            academicYears: [
                '2024-2025',
                '2023-2024',
                '2022-2023',
                '2021-2022'
            ],
            semesters: [
                { id: 1, name: 'الفصل الدراسي الأول', startDate: '2024-09-01', endDate: '2025-01-15' },
                { id: 2, name: 'الفصل الدراسي الثاني', startDate: '2025-02-01', endDate: '2025-06-15' }
            ],
            gradeSettings: {
                grades1to4: {
                    total: 50,
                    components: {
                        oral1: { max: 10, label: 'الأعمال الشفوية (الفترة الأولى)' },
                        oral2: { max: 10, label: 'الأعمال الشفوية (الفترة الثانية)' },
                        practical1: { max: 10, label: 'الأنشطة العملية (الفترة الأولى)' },
                        practical2: { max: 10, label: 'الأنشطة العملية (الفترة الثانية)' },
                        project: { max: 10, label: 'المشروع' }
                    }
                },
                grades5to10: {
                    total: 100,
                    components: {
                        oral1: { max: 10, label: 'الأعمال الشفوية (الفترة الأولى)' },
                        oral2: { max: 10, label: 'الأعمال الشفوية (الفترة الثانية)' },
                        practical1: { max: 20, label: 'الأنشطة العملية (الفترة الأولى)' },
                        practical2: { max: 20, label: 'الأنشطة العملية (الفترة الثانية)' },
                        project: { max: 20, label: 'المشروع' },
                        shortTest: { max: 20, label: 'الاختبار القصير' }
                    }
                },
                grades11to12: {
                    total: 100,
                    components: {
                        oral1: { max: 5, label: 'الأعمال الشفوية (الفترة الأولى)' },
                        oral2: { max: 5, label: 'الأعمال الشفوية (الفترة الثانية)' },
                        practical1: { max: 20, label: 'الأنشطة العملية (الفترة الأولى)' },
                        practical2: { max: 20, label: 'الأنشطة العملية (الفترة الثانية)' },
                        project: { max: 20, label: 'المشروع' },
                        shortTest: { max: 20, label: 'الاختبار القصير' },
                        finalExam: { max: 30, label: 'الاختبار النهائي' }
                    }
                }
            },
            reportSettings: {
                schoolLogo: '',
                schoolHeader: 'وزارة التعليم - المملكة العربية السعودية',
                reportFooter: 'تم إنشاء هذا التقرير بواسطة نظام تقويم تقنية المعلومات',
                includeCharts: true,
                includeStatistics: true,
                defaultExportFormat: 'xlsx'
            },
            uiSettings: {
                itemsPerPage: 20,
                autoSave: true,
                autoSaveInterval: 30000,
                showNotifications: true,
                animationsEnabled: true,
                compactMode: false
            },
            securitySettings: {
                requireBackupConfirmation: true,
                autoBackupEnabled: false,
                autoBackupInterval: 'weekly',
                maxBackupFiles: 10
            }
        };
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Save settings button
        const saveBtn = document.getElementById('saveSettingsBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSettings());
        }

        // Reset settings button
        const resetBtn = document.getElementById('resetSettingsBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetSettings());
        }
    }

    async initialize() {
        try {
            await this.loadSettings();
            this.renderSettingsInterface();
        } catch (error) {
            console.error('Error initializing settings manager:', error);
            Utils.showNotification('خطأ في تحميل الإعدادات', 'error');
        }
    }

    async loadSettings() {
        try {
            this.settings = await db.getSettings() || this.defaultSettings;
            
            // Merge with defaults to ensure all settings exist
            this.settings = { ...this.defaultSettings, ...this.settings };
        } catch (error) {
            console.error('Error loading settings:', error);
            this.settings = { ...this.defaultSettings };
        }
    }

    renderSettingsInterface() {
        const container = document.querySelector('#settings .settings-container');
        if (!container) return;

        container.innerHTML = `
            <div class="settings-dashboard">
                <!-- Academic Year Settings -->
                <div class="settings-section">
                    <div class="settings-section-header">
                        <h3><i class="fas fa-calendar-alt"></i> إعدادات العام الدراسي</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-group">
                            <label for="currentAcademicYear">العام الدراسي الحالي</label>
                            <select id="currentAcademicYear" class="form-select">
                                ${this.settings.academicYears.map(year => 
                                    `<option value="${year}" ${year === this.settings.currentAcademicYear ? 'selected' : ''}>${year}</option>`
                                ).join('')}
                            </select>
                            <small class="setting-description">اختر العام الدراسي الحالي الذي سيتم استخدامه افتراضياً</small>
                        </div>

                        <div class="setting-group">
                            <label for="currentSemester">الفصل الدراسي الحالي</label>
                            <select id="currentSemester" class="form-select">
                                <option value="1" ${this.settings.currentSemester === 1 ? 'selected' : ''}>الفصل الأول</option>
                                <option value="2" ${this.settings.currentSemester === 2 ? 'selected' : ''}>الفصل الثاني</option>
                            </select>
                            <small class="setting-description">اختر الفصل الدراسي الحالي</small>
                        </div>

                        <div class="setting-group full-width">
                            <label>إدارة الأعوام الدراسية</label>
                            <div class="academic-years-manager">
                                <div class="years-list">
                                    ${this.settings.academicYears.map((year, index) => `
                                        <div class="year-item" data-year="${year}">
                                            <span class="year-text">${year}</span>
                                            <button class="btn-icon delete-year" data-year="${year}" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    `).join('')}
                                </div>
                                <div class="add-year-form">
                                    <input type="text" id="newAcademicYear" placeholder="مثال: 2025-2026" class="form-input">
                                    <button id="addAcademicYear" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> إضافة عام دراسي
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Semester Settings -->
                <div class="settings-section">
                    <div class="settings-section-header">
                        <h3><i class="fas fa-clock"></i> إعدادات الفصول الدراسية</h3>
                    </div>
                    <div class="semesters-grid">
                        ${this.settings.semesters.map(semester => `
                            <div class="semester-card">
                                <h4>${semester.name}</h4>
                                <div class="semester-dates">
                                    <div class="date-group">
                                        <label>تاريخ البداية</label>
                                        <input type="date" 
                                               value="${semester.startDate}" 
                                               data-semester="${semester.id}" 
                                               data-field="startDate"
                                               class="form-input semester-date">
                                    </div>
                                    <div class="date-group">
                                        <label>تاريخ النهاية</label>
                                        <input type="date" 
                                               value="${semester.endDate}" 
                                               data-semester="${semester.id}" 
                                               data-field="endDate"
                                               class="form-input semester-date">
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Grade Structure Settings -->
                <div class="settings-section">
                    <div class="settings-section-header">
                        <h3><i class="fas fa-graduation-cap"></i> إعدادات هيكل الدرجات</h3>
                    </div>
                    <div class="grade-structure-tabs">
                        <div class="tabs-nav">
                            <button class="tab-btn active" data-grade-range="grades1to4">الصفوف 1-4</button>
                            <button class="tab-btn" data-grade-range="grades5to10">الصفوف 5-10</button>
                            <button class="tab-btn" data-grade-range="grades11to12">الصفوف 11-12</button>
                        </div>
                        <div class="tabs-content">
                            ${this.renderGradeStructureTabs()}
                        </div>
                    </div>
                </div>

                <!-- Report Settings -->
                <div class="settings-section">
                    <div class="settings-section-header">
                        <h3><i class="fas fa-file-alt"></i> إعدادات التقارير</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-group">
                            <label for="schoolHeader">رأس التقرير</label>
                            <input type="text" 
                                   id="schoolHeader" 
                                   value="${this.settings.reportSettings.schoolHeader}"
                                   class="form-input"
                                   placeholder="وزارة التعليم - المملكة العربية السعودية">
                            <small class="setting-description">النص الذي سيظهر في أعلى التقارير</small>
                        </div>

                        <div class="setting-group">
                            <label for="reportFooter">تذييل التقرير</label>
                            <input type="text" 
                                   id="reportFooter" 
                                   value="${this.settings.reportSettings.reportFooter}"
                                   class="form-input"
                                   placeholder="تم إنشاء هذا التقرير بواسطة نظام تقويم تقنية المعلومات">
                            <small class="setting-description">النص الذي سيظهر في أسفل التقارير</small>
                        </div>

                        <div class="setting-group">
                            <label for="defaultExportFormat">تنسيق التصدير الافتراضي</label>
                            <select id="defaultExportFormat" class="form-select">
                                <option value="xlsx" ${this.settings.reportSettings.defaultExportFormat === 'xlsx' ? 'selected' : ''}>Excel (.xlsx)</option>
                                <option value="pdf" ${this.settings.reportSettings.defaultExportFormat === 'pdf' ? 'selected' : ''}>PDF (.pdf)</option>
                                <option value="csv" ${this.settings.reportSettings.defaultExportFormat === 'csv' ? 'selected' : ''}>CSV (.csv)</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="includeCharts" 
                                           ${this.settings.reportSettings.includeCharts ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    تضمين الرسوم البيانية في التقارير
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="includeStatistics" 
                                           ${this.settings.reportSettings.includeStatistics ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    تضمين الإحصائيات في التقارير
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- UI Settings -->
                <div class="settings-section">
                    <div class="settings-section-header">
                        <h3><i class="fas fa-desktop"></i> إعدادات واجهة المستخدم</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-group">
                            <label for="itemsPerPage">عدد العناصر في الصفحة</label>
                            <input type="number" 
                                   id="itemsPerPage" 
                                   value="${this.settings.uiSettings.itemsPerPage}"
                                   min="10" 
                                   max="100" 
                                   step="5"
                                   class="form-input">
                            <small class="setting-description">عدد الطلاب أو المدارس المعروضة في كل صفحة</small>
                        </div>

                        <div class="setting-group">
                            <label for="autoSaveInterval">فترة الحفظ التلقائي (بالثواني)</label>
                            <input type="number" 
                                   id="autoSaveInterval" 
                                   value="${this.settings.uiSettings.autoSaveInterval / 1000}"
                                   min="10" 
                                   max="300" 
                                   step="5"
                                   class="form-input">
                            <small class="setting-description">كم ثانية بين كل حفظ تلقائي للبيانات</small>
                        </div>

                        <div class="setting-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="autoSave" 
                                           ${this.settings.uiSettings.autoSave ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    تفعيل الحفظ التلقائي
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="showNotifications" 
                                           ${this.settings.uiSettings.showNotifications ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    إظهار الإشعارات
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="animationsEnabled" 
                                           ${this.settings.uiSettings.animationsEnabled ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    تفعيل الرسوم المتحركة
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="settings-section">
                    <div class="settings-section-header">
                        <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان والنسخ الاحتياطي</h3>
                    </div>
                    <div class="settings-grid">
                        <div class="setting-group">
                            <label for="autoBackupInterval">فترة النسخ الاحتياطي التلقائي</label>
                            <select id="autoBackupInterval" class="form-select">
                                <option value="daily" ${this.settings.securitySettings.autoBackupInterval === 'daily' ? 'selected' : ''}>يومياً</option>
                                <option value="weekly" ${this.settings.securitySettings.autoBackupInterval === 'weekly' ? 'selected' : ''}>أسبوعياً</option>
                                <option value="monthly" ${this.settings.securitySettings.autoBackupInterval === 'monthly' ? 'selected' : ''}>شهرياً</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label for="maxBackupFiles">الحد الأقصى لملفات النسخ الاحتياطي</label>
                            <input type="number" 
                                   id="maxBackupFiles" 
                                   value="${this.settings.securitySettings.maxBackupFiles}"
                                   min="1" 
                                   max="50" 
                                   class="form-input">
                            <small class="setting-description">عدد ملفات النسخ الاحتياطي التي سيتم الاحتفاظ بها</small>
                        </div>

                        <div class="setting-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="autoBackupEnabled" 
                                           ${this.settings.securitySettings.autoBackupEnabled ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    تفعيل النسخ الاحتياطي التلقائي
                                </label>
                            </div>
                        </div>

                        <div class="setting-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" 
                                           id="requireBackupConfirmation" 
                                           ${this.settings.securitySettings.requireBackupConfirmation ? 'checked' : ''}>
                                    <span class="checkmark"></span>
                                    طلب تأكيد قبل إنشاء النسخ الاحتياطي
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.addSettingsEventListeners();
    }

    renderGradeStructureTabs() {
        const gradeRanges = ['grades1to4', 'grades5to10', 'grades11to12'];
        const rangeNames = {
            'grades1to4': 'الصفوف 1-4',
            'grades5to10': 'الصفوف 5-10',
            'grades11to12': 'الصفوف 11-12'
        };

        return gradeRanges.map((range, index) => {
            const gradeSettings = this.settings.gradeSettings[range];
            const isActive = index === 0 ? 'active' : '';

            return `
                <div class="tab-pane ${isActive}" data-grade-range="${range}">
                    <div class="grade-structure-content">
                        <div class="total-score-setting">
                            <label>المجموع الكلي للدرجات</label>
                            <input type="number"
                                   value="${gradeSettings.total}"
                                   data-range="${range}"
                                   data-field="total"
                                   class="form-input grade-total-input"
                                   min="50"
                                   max="200">
                        </div>

                        <div class="components-grid">
                            <h4>مكونات التقييم</h4>
                            ${Object.entries(gradeSettings.components).map(([key, component]) => `
                                <div class="component-setting">
                                    <div class="component-header">
                                        <label>${component.label}</label>
                                        <input type="number"
                                               value="${component.max}"
                                               data-range="${range}"
                                               data-component="${key}"
                                               data-field="max"
                                               class="form-input component-max-input"
                                               min="1"
                                               max="50">
                                    </div>
                                    <input type="text"
                                           value="${component.label}"
                                           data-range="${range}"
                                           data-component="${key}"
                                           data-field="label"
                                           class="form-input component-label-input"
                                           placeholder="اسم المكون">
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    addSettingsEventListeners() {
        // Academic year management
        document.getElementById('addAcademicYear')?.addEventListener('click', () => {
            this.addAcademicYear();
        });

        document.querySelectorAll('.delete-year').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.deleteAcademicYear(e.target.dataset.year);
            });
        });

        // Grade structure tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchGradeTab(e.target.dataset.gradeRange);
            });
        });

        // Semester dates
        document.querySelectorAll('.semester-date').forEach(input => {
            input.addEventListener('change', (e) => {
                this.updateSemesterDate(e.target);
            });
        });

        // Grade structure inputs
        document.querySelectorAll('.grade-total-input, .component-max-input, .component-label-input').forEach(input => {
            input.addEventListener('change', (e) => {
                this.updateGradeStructure(e.target);
            });
        });

        // Auto-save interval conversion
        document.getElementById('autoSaveInterval')?.addEventListener('change', (e) => {
            // Convert seconds to milliseconds
            const seconds = parseInt(e.target.value);
            this.settings.uiSettings.autoSaveInterval = seconds * 1000;
        });
    }

    addAcademicYear() {
        const input = document.getElementById('newAcademicYear');
        const year = input.value.trim();

        if (!year) {
            Utils.showNotification('يرجى إدخال العام الدراسي', 'error');
            return;
        }

        // Validate format (YYYY-YYYY)
        const yearPattern = /^\d{4}-\d{4}$/;
        if (!yearPattern.test(year)) {
            Utils.showNotification('تنسيق العام الدراسي يجب أن يكون مثل: 2025-2026', 'error');
            return;
        }

        if (this.settings.academicYears.includes(year)) {
            Utils.showNotification('هذا العام الدراسي موجود بالفعل', 'error');
            return;
        }

        this.settings.academicYears.push(year);
        this.settings.academicYears.sort().reverse(); // Sort descending

        input.value = '';
        this.renderSettingsInterface();
        Utils.showNotification('تم إضافة العام الدراسي بنجاح', 'success');
    }

    deleteAcademicYear(year) {
        if (this.settings.academicYears.length <= 1) {
            Utils.showNotification('يجب الاحتفاظ بعام دراسي واحد على الأقل', 'error');
            return;
        }

        if (year === this.settings.currentAcademicYear) {
            Utils.showNotification('لا يمكن حذف العام الدراسي الحالي', 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف العام الدراسي ${year}؟`)) {
            this.settings.academicYears = this.settings.academicYears.filter(y => y !== year);
            this.renderSettingsInterface();
            Utils.showNotification('تم حذف العام الدراسي بنجاح', 'success');
        }
    }

    switchGradeTab(gradeRange) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-grade-range="${gradeRange}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.querySelector(`.tab-pane[data-grade-range="${gradeRange}"]`).classList.add('active');
    }

    updateSemesterDate(input) {
        const semesterId = parseInt(input.dataset.semester);
        const field = input.dataset.field;
        const value = input.value;

        const semester = this.settings.semesters.find(s => s.id === semesterId);
        if (semester) {
            semester[field] = value;
        }
    }

    updateGradeStructure(input) {
        const range = input.dataset.range;
        const component = input.dataset.component;
        const field = input.dataset.field;
        const value = input.type === 'number' ? parseFloat(input.value) : input.value;

        if (component) {
            // Update component
            this.settings.gradeSettings[range].components[component][field] = value;
        } else if (field === 'total') {
            // Update total
            this.settings.gradeSettings[range].total = value;
        }

        // Validate total matches sum of components
        if (input.type === 'number') {
            this.validateGradeStructure(range);
        }
    }

    validateGradeStructure(range) {
        const gradeSettings = this.settings.gradeSettings[range];
        const componentsSum = Object.values(gradeSettings.components)
            .reduce((sum, comp) => sum + comp.max, 0);

        if (componentsSum !== gradeSettings.total) {
            const totalInput = document.querySelector(`[data-range="${range}"][data-field="total"]`);
            if (totalInput) {
                totalInput.style.borderColor = '#dc3545';
                totalInput.title = `مجموع المكونات (${componentsSum}) لا يساوي المجموع الكلي (${gradeSettings.total})`;
            }
        } else {
            const totalInput = document.querySelector(`[data-range="${range}"][data-field="total"]`);
            if (totalInput) {
                totalInput.style.borderColor = '#28a745';
                totalInput.title = 'المجموع صحيح';
            }
        }
    }

    async saveSettings() {
        try {
            // Collect all form data
            this.collectFormData();

            // Validate settings
            const validation = this.validateSettings();
            if (!validation.isValid) {
                Utils.showNotification(validation.message, 'error');
                return;
            }

            // Save to database
            await db.updateSettings(this.settings);

            // Update app settings
            if (window.app) {
                window.app.settings = this.settings;
            }

            Utils.showNotification('تم حفظ الإعدادات بنجاح', 'success');

            // Refresh relevant managers
            this.refreshManagers();

        } catch (error) {
            console.error('Error saving settings:', error);
            Utils.showNotification('خطأ في حفظ الإعدادات', 'error');
        }
    }

    collectFormData() {
        // Academic year settings
        this.settings.currentAcademicYear = document.getElementById('currentAcademicYear')?.value || this.settings.currentAcademicYear;
        this.settings.currentSemester = parseInt(document.getElementById('currentSemester')?.value) || this.settings.currentSemester;

        // Report settings
        this.settings.reportSettings.schoolHeader = document.getElementById('schoolHeader')?.value || this.settings.reportSettings.schoolHeader;
        this.settings.reportSettings.reportFooter = document.getElementById('reportFooter')?.value || this.settings.reportSettings.reportFooter;
        this.settings.reportSettings.defaultExportFormat = document.getElementById('defaultExportFormat')?.value || this.settings.reportSettings.defaultExportFormat;
        this.settings.reportSettings.includeCharts = document.getElementById('includeCharts')?.checked || false;
        this.settings.reportSettings.includeStatistics = document.getElementById('includeStatistics')?.checked || false;

        // UI settings
        this.settings.uiSettings.itemsPerPage = parseInt(document.getElementById('itemsPerPage')?.value) || this.settings.uiSettings.itemsPerPage;
        this.settings.uiSettings.autoSave = document.getElementById('autoSave')?.checked || false;
        this.settings.uiSettings.showNotifications = document.getElementById('showNotifications')?.checked || false;
        this.settings.uiSettings.animationsEnabled = document.getElementById('animationsEnabled')?.checked || false;

        // Security settings
        this.settings.securitySettings.autoBackupInterval = document.getElementById('autoBackupInterval')?.value || this.settings.securitySettings.autoBackupInterval;
        this.settings.securitySettings.maxBackupFiles = parseInt(document.getElementById('maxBackupFiles')?.value) || this.settings.securitySettings.maxBackupFiles;
        this.settings.securitySettings.autoBackupEnabled = document.getElementById('autoBackupEnabled')?.checked || false;
        this.settings.securitySettings.requireBackupConfirmation = document.getElementById('requireBackupConfirmation')?.checked || false;

        // Update timestamp
        this.settings.updatedAt = new Date().toISOString();
    }

    validateSettings() {
        // Validate academic years
        if (!this.settings.academicYears || this.settings.academicYears.length === 0) {
            return { isValid: false, message: 'يجب وجود عام دراسي واحد على الأقل' };
        }

        if (!this.settings.academicYears.includes(this.settings.currentAcademicYear)) {
            return { isValid: false, message: 'العام الدراسي الحالي غير موجود في قائمة الأعوام' };
        }

        // Validate grade structures
        for (const [range, gradeSettings] of Object.entries(this.settings.gradeSettings)) {
            const componentsSum = Object.values(gradeSettings.components)
                .reduce((sum, comp) => sum + comp.max, 0);

            if (componentsSum !== gradeSettings.total) {
                return {
                    isValid: false,
                    message: `مجموع مكونات ${range} (${componentsSum}) لا يساوي المجموع الكلي (${gradeSettings.total})`
                };
            }
        }

        return { isValid: true };
    }

    refreshManagers() {
        // Refresh grades manager if active
        if (window.gradesManager && window.gradesManager.initialized) {
            window.gradesManager.updateGradeStructure();
        }

        // Update dashboard if active
        if (window.app && window.app.currentSection === 'dashboard') {
            window.app.loadDashboard();
        }
    }

    async resetSettings() {
        if (confirm('هل أنت متأكد من استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع التخصيصات الحالية.')) {
            try {
                this.settings = { ...this.defaultSettings };
                await db.updateSettings(this.settings);
                this.renderSettingsInterface();
                Utils.showNotification('تم استعادة الإعدادات الافتراضية بنجاح', 'success');
            } catch (error) {
                console.error('Error resetting settings:', error);
                Utils.showNotification('خطأ في استعادة الإعدادات', 'error');
            }
        }
    }
}

// Create global settings manager instance
const settingsManager = new SettingsManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize settings manager when settings section is first shown
    const settingsSection = document.getElementById('settings');
    if (settingsSection) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (settingsSection.classList.contains('active') && !settingsManager.initialized) {
                        settingsManager.initialize();
                        settingsManager.initialized = true;
                    }
                }
            });
        });

        observer.observe(settingsSection, { attributes: true });
    }
});

// Make settingsManager available globally
window.settingsManager = settingsManager;
