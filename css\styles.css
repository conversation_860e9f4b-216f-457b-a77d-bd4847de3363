/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 2rem;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.nav-btn {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: inherit;
    font-size: 0.9rem;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: rgba(255,255,255,0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 100px);
}

.section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    font-size: 1.8rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Dashboard Styles */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.dashboard-card:nth-child(1) .card-icon { background: #667eea; }
.dashboard-card:nth-child(2) .card-icon { background: #f093fb; }
.dashboard-card:nth-child(3) .card-icon { background: #4facfe; }
.dashboard-card:nth-child(4) .card-icon { background: #43e97b; }

.card-content h3 {
    font-size: 1rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.card-number {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.card-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
}

/* Student ID Container */
.student-id-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.student-id-container .form-input {
    flex: 1;
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}

.student-id-container .form-input:read-only {
    cursor: not-allowed;
    border-color: #e9ecef;
}

.refresh-student-id {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-student-id:hover {
    background: #5a6fd8;
    transform: scale(1.05);
}

.refresh-student-id:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.refresh-student-id i {
    font-size: 0.9rem;
}

/* Filters */
.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.filters .form-select,
.filters .form-input {
    min-width: 200px;
}

/* Table Styles */
.table {
    width: 100%;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.table th,
.table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .nav {
        justify-content: center;
    }
    
    .nav-btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filters {
        flex-direction: column;
    }
    
    .filters .form-select,
    .filters .form-input {
        min-width: auto;
    }
    
    .table {
        font-size: 0.9rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.w-100 { width: 100%; }

/* Additional Styles for Enhanced UI */

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #666;
}

.empty-state i {
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

.empty-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.spinner i {
    font-size: 2rem;
    color: #667eea;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    z-index: 1001;
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.notification-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.notification-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.notification-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    margin-left: auto;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.notification-close:hover {
    background: rgba(0,0,0,0.1);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Schools Grid */
.schools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.school-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.school-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.school-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.school-info h3 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.school-code {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    color: #666;
}

.school-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.3s ease;
    color: #666;
}

.btn-icon:hover {
    background: #f8f9fa;
}

.btn-icon.edit-school:hover {
    background: #e3f2fd;
    color: #1976d2;
}

.btn-icon.delete-school:hover {
    background: #ffebee;
    color: #d32f2f;
}

.school-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.school-detail i {
    width: 16px;
    color: #999;
}

.school-footer {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.school-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: #666;
}

.school-quick-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin: 2rem 0;
}

.pagination-btn {
    background: white;
    border: 1px solid #ddd;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
}

.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.pagination-current {
    background: #667eea;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-weight: 600;
}

.pagination-dots {
    padding: 0.5rem;
    color: #999;
}

/* Table Enhancements */
.table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.table-info {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #eee;
    font-size: 0.9rem;
    color: #666;
    text-align: center;
}

/* Grades Table Specific */
.grades-table-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.grades-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.grades-header h3 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.grades-info {
    display: flex;
    gap: 2rem;
    font-size: 0.9rem;
    color: #666;
}

.table-responsive {
    overflow-x: auto;
    margin-bottom: 1.5rem;
}

.grades-table {
    min-width: 800px;
}

.grades-table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    text-align: center;
    padding: 0.75rem 0.5rem;
    border: 1px solid #dee2e6;
    color: #495057;
    font-size: 0.9rem;
    white-space: nowrap;
}

.grades-table td {
    text-align: center;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    vertical-align: middle;
    position: relative;
}

/* تحسين مظهر خلايا إدخال الدرجات */
.grades-table td:has(.grade-input) {
    padding: 0.25rem;
    background: #fafbff;
}

.grades-table td:has(.grade-input:focus) {
    background: #f0f4ff;
    box-shadow: inset 0 0 0 1px rgba(102, 126, 234, 0.3);
}

/* تحسين مظهر صفوف الطلاب */
.grades-table tbody tr:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

.grades-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.grades-table .student-name {
    text-align: right;
    font-weight: 500;
    min-width: 150px;
}

.grade-input {
    width: 60px;
    padding: 0.25rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 0.9rem;
    font-family: inherit;
    background: white;
    transition: all 0.3s ease;
}

/* إزالة الأسهم من حقول الأرقام */
.grade-input::-webkit-outer-spin-button,
.grade-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
.grade-input[type=number] {
    -moz-appearance: textfield;
}

/* تحسين التصميم عند التركيز */
.grade-input:hover {
    border-color: #667eea;
    box-shadow: 0 0 0 1px rgba(102, 126, 234, 0.1);
}

.grade-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    background: #fafbff;
}

/* تحسين مظهر المربعات الفارغة */
.grade-input:placeholder-shown {
    background: #f8f9fa;
    border-color: #e9ecef;
}

/* تحسين مظهر المربعات التي تحتوي على قيم */
.grade-input:not(:placeholder-shown) {
    background: white;
    border-color: #28a745;
    font-weight: 600;
}

/* تحسين مظهر المربعات عند الخطأ */
.grade-input.error {
    border-color: #dc3545;
    background: #fff5f5;
    animation: shake 0.3s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* تحسين مظهر المربعات المعطلة */
.grade-input:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    border-color: #ced4da;
}

.grade-input.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.total-score {
    font-weight: 600;
    color: #333;
    text-align: center;
    font-size: 1.1rem;
}

.grade-description {
    text-align: center;
    font-weight: 500;
    color: #555;
}

.grade-level {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    color: white;
}

/* Grade level colors - using attribute selectors instead of :contains */
.grade-level {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    color: white;
    text-align: center;
    min-width: 40px;
}

.grade-level[data-level="أ"] { background: #28a745; }
.grade-level[data-level="ب"] { background: #17a2b8; }
.grade-level[data-level="ج"] { background: #ffc107; color: #333; }
.grade-level[data-level="د"] { background: #fd7e14; }
.grade-level[data-level="هـ"] { background: #dc3545; }
.grade-level[data-level="راسب"] { background: #6c757d; }
.grade-level[data-level="ز"] { background: #6c757d; }

.grades-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.max-score {
    font-size: 0.8rem;
    color: #666;
    font-weight: normal;
}

/* Row states */
.saved {
    background: #d4edda !important;
    animation: fadeToNormal 2s ease-out;
}

@keyframes fadeToNormal {
    from { background: #d4edda !important; }
    to { background: transparent; }
}

/* تحسينات إضافية لمربعات الإدخال */
.grade-input::-webkit-input-placeholder {
    color: #adb5bd;
    font-style: italic;
}

.grade-input::-moz-placeholder {
    color: #adb5bd;
    font-style: italic;
}

.grade-input::placeholder {
    color: #adb5bd;
    font-style: italic;
}

/* تأثير عند الحفظ */
.grade-input.saving {
    background: #fff3cd;
    border-color: #ffc107;
    animation: pulse 1s ease-in-out;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* تحسين مظهر الأزرار في الجدول */
.grades-table .btn-icon {
    padding: 0.25rem;
    margin: 0 0.125rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.grades-table .btn-icon:hover {
    transform: scale(1.1);
}

/* تحسين مظهر خلايا المجموع والمستوى */
.total-score {
    font-weight: 700;
    color: #495057;
    text-align: center;
    font-size: 1.1rem;
    background: rgba(40, 167, 69, 0.1);
    border-left: 3px solid #28a745;
}

.grade-description {
    text-align: center;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
    font-style: italic;
}

/* تحسين مظهر رؤوس الأعمدة */
.grades-table th.max-score {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: normal;
    padding: 0.25rem;
    background: #e9ecef;
}

/* تحسين التباعد والتخطيط */
.grades-table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.grades-table th:first-child,
.grades-table td:first-child {
    border-right: none;
}

.grades-table th:last-child,
.grades-table td:last-child {
    border-left: none;
}

.grades-table tbody tr:last-child td {
    border-bottom: none;
}

/* Reports Styles */
.reports-dashboard {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.report-filters {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.report-types {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.report-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.report-type-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid transparent;
}

.report-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.report-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.report-type-card h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.report-type-card p {
    margin: 0 0 1.5rem 0;
    color: #666;
    font-size: 0.9rem;
}

.report-output {
    margin-top: 2rem;
}

.report-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.report-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.report-header h2 {
    margin: 0 0 1rem 0;
    color: #333;
}

.report-info {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: #666;
}

.report-summary {
    margin-bottom: 2rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.summary-item .label {
    font-weight: 600;
    color: #333;
}

.summary-item .value {
    font-weight: 700;
    color: #667eea;
    font-size: 1.1rem;
}

.report-table {
    margin-bottom: 2rem;
}

.report-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

/* Statistics Styles */
.statistics-dashboard {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.statistics-filters {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-overview {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.stat-card:nth-child(1) .stat-icon { background: #667eea; }
.stat-card:nth-child(2) .stat-icon { background: #f093fb; }
.stat-card:nth-child(3) .stat-icon { background: #4facfe; }
.stat-card:nth-child(4) .stat-icon { background: #43e97b; }

.stat-content h3 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: #666;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

.charts-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.chart-container {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    height: 300px;
}

.chart-header {
    margin-bottom: 1rem;
    text-align: center;
}

.chart-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.chart-container canvas {
    max-height: 250px;
}

.detailed-stats {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.tab-btn {
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    color: #666;
    font-family: inherit;
}

.tab-btn:hover {
    background: #f8f9fa;
}

.tab-btn.active {
    background: #667eea;
    color: white;
}

.tab-content {
    min-height: 300px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.distribution-chart {
    margin-top: 1rem;
}

.distribution-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.level {
    min-width: 30px;
    font-weight: 600;
}

.bar-container {
    flex: 1;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
}

.bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.count {
    min-width: 80px;
    text-align: right;
    font-size: 0.9rem;
    color: #666;
}

/* Analysis Styles */
.performance-analysis {
    padding: 1rem;
}

.analysis-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 1rem;
}

.analysis-section {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
}

.analysis-section h5 {
    margin: 0 0 1rem 0;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.strengths-list,
.weaknesses-list,
.recommendations-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.strengths-list li {
    padding: 0.5rem 0;
    border-left: 3px solid #28a745;
    padding-left: 1rem;
    margin-bottom: 0.5rem;
    background: #d4edda;
    border-radius: 4px;
}

.weaknesses-list li {
    padding: 0.5rem 0;
    border-left: 3px solid #dc3545;
    padding-left: 1rem;
    margin-bottom: 0.5rem;
    background: #f8d7da;
    border-radius: 4px;
}

.recommendations-list li {
    padding: 0.5rem 0;
    border-left: 3px solid #17a2b8;
    padding-left: 1rem;
    margin-bottom: 0.5rem;
    background: #d1ecf1;
    border-radius: 4px;
}

.key-metrics {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #ddd;
}

.metric-label {
    font-weight: 600;
    color: #333;
}

.metric-value {
    font-weight: 700;
    color: #667eea;
}

/* Settings Styles */
.settings-dashboard {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.settings-section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.settings-section-header h3 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-section-header i {
    color: #667eea;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.setting-group.full-width {
    grid-column: 1 / -1;
}

.setting-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.setting-description {
    color: #666;
    font-size: 0.8rem;
    font-style: italic;
}

.form-input,
.form-select {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Academic Years Manager */
.academic-years-manager {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.years-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.year-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f8f9fa;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.year-text {
    font-weight: 500;
    color: #495057;
}

.delete-year {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.delete-year:hover {
    background: #f8d7da;
}

.add-year-form {
    display: flex;
    gap: 0.5rem;
    align-items: end;
}

.add-year-form input {
    flex: 1;
}

/* Semesters Grid */
.semesters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.semester-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.semester-card h4 {
    margin: 0 0 1rem 0;
    color: #495057;
    text-align: center;
}

.semester-dates {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.date-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.date-group label {
    font-size: 0.8rem;
    color: #666;
}

/* Grade Structure Tabs */
.grade-structure-tabs {
    width: 100%;
}

.tabs-nav {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.tab-btn {
    background: none;
    border: none;
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    color: #666;
    font-family: inherit;
    font-weight: 500;
}

.tab-btn:hover {
    background: #f8f9fa;
}

.tab-btn.active {
    background: #667eea;
    color: white;
}

.tabs-content {
    min-height: 300px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.grade-structure-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.total-score-setting {
    background: #e3f2fd;
    padding: 1rem;
    border-radius: 6px;
    border-left: 4px solid #2196f3;
}

.total-score-setting label {
    color: #1976d2;
    font-weight: 600;
}

.grade-total-input {
    margin-top: 0.5rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.components-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.components-grid h4 {
    margin: 0;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
}

.component-setting {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.component-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.component-header label {
    flex: 1;
    margin: 0;
}

.component-max-input {
    width: 80px;
    text-align: center;
    font-weight: 600;
}

.component-label-input {
    margin-top: 0.5rem;
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    margin: 0;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 0.8rem;
}

/* Section Actions */
.section-actions {
    display: flex;
    gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }

    .semesters-grid {
        grid-template-columns: 1fr;
    }

    .semester-dates {
        grid-template-columns: 1fr;
    }

    .tabs-nav {
        flex-direction: column;
    }

    .component-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .add-year-form {
        flex-direction: column;
    }

    .section-actions {
        flex-direction: column;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .report-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .chart-container {
        break-inside: avoid;
    }

    .table {
        font-size: 0.8rem;
    }

    .report-header {
        border-bottom: 2px solid #333;
    }
}
