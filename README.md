# استمارة تقويم تقنية المعلومات

نظام شامل لإدارة درجات الطلاب في مادة تقنية المعلومات للصفوف من الأول حتى الثاني عشر.

## المميزات

### 🏫 إدارة المدارس
- إضافة وتعديل بيانات المدارس
- تتبع معلومات الاتصال والعناوين
- إحصائيات شاملة لكل مدرسة

### 👥 إدارة الطلاب
- تسجيل الطلاب يدوياً أو عبر استيراد ملفات Excel
- تصنيف حسب المدرسة والصف والشعبة
- بحث وفلترة متقدمة

### 📊 نظام الدرجات
- إدخال درجات مخصص لكل مرحلة دراسية:
  - **الصفوف 1-4**: الأعمال الشفوية، الأنشطة العملية، المشروع (المجموع: 50)
  - **الصفوف 5-10**: الأعمال الشفوية، الأنشطة العملية، المشروع، الاختبار القصير (المجموع: 100)
  - **الصفوف 11-12**: الأعمال الشفوية، الأنشطة العملية، المشروع، الاختبار القصير، الاختبار النهائي (المجموع: 100)
- حساب تلقائي للمجموع والمستوى والعبارة الوصفية
- حفظ وتصدير البيانات

### 📈 التقارير والإحصائيات
- تقارير فصلية وسنوية شاملة
- إحصائيات مفصلة للأداء والتوزيع
- رسوم بيانية تفاعلية
- تحليل نقاط القوة والضعف
- تقارير الطلاب المتفوقين والمتعثرين

### 🎨 التصميم والواجهة
- تصميم متجاوب يدعم جميع أحجام الشاشات
- دعم كامل للغة العربية مع تخطيط RTL
- واجهة مستخدم حديثة وسهلة الاستخدام
- ألوان وخطوط محسنة للقراءة

## التقنيات المستخدمة

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Backend**: Node.js مع Electron
- **قاعدة البيانات**: ملفات JSON محلية
- **الرسوم البيانية**: Chart.js
- **استيراد/تصدير**: SheetJS (XLSX)
- **التصميم**: CSS Grid, Flexbox, RTL Support

## متطلبات النظام

- **Windows**: Windows 10 أو أحدث
- **macOS**: macOS 10.14 أو أحدث  
- **Linux**: Ubuntu 18.04 أو أحدث
- **الذاكرة**: 4 GB RAM كحد أدنى
- **التخزين**: 500 MB مساحة فارغة

## التثبيت والتشغيل

### 1. تشغيل التطبيق كموقع ويب

```bash
# فتح ملف index.html في المتصفح مباشرة
# أو استخدام خادم محلي
python -m http.server 8000
# ثم زيارة http://localhost:8000
```

### 2. تشغيل كتطبيق سطح مكتب (Electron)

```bash
# تثبيت Node.js أولاً من https://nodejs.org

# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير
npm run dev

# أو تشغيل التطبيق العادي
npm start
```

### 3. بناء التطبيق للتوزيع

```bash
# بناء لنظام Windows
npm run build-win

# بناء لنظام macOS
npm run build-mac

# بناء لنظام Linux
npm run build-linux

# بناء لجميع الأنظمة
npm run build
```

## دليل الاستخدام

### البدء السريع

1. **إضافة مدرسة جديدة**
   - انتقل إلى قسم "المدارس"
   - اضغط على "إضافة مدرسة"
   - أدخل بيانات المدرسة واحفظ

2. **إضافة الطلاب**
   - انتقل إلى قسم "الطلاب"
   - اختر "إضافة طالب" للإدخال اليدوي
   - أو "استيراد من Excel" لرفع قائمة طلاب

3. **إدخال الدرجات**
   - انتقل إلى قسم "الدرجات"
   - اختر المدرسة والصف والشعبة
   - أدخل الدرجات في الجدول
   - احفظ البيانات

4. **عرض التقارير**
   - انتقل إلى قسم "التقارير"
   - اختر نوع التقرير المطلوب
   - حدد المعايير والفلاتر
   - اطبع أو صدّر التقرير

### نظام التقييم

#### الصفوف 1-4
- الأعمال الشفوية: فترتان × 10 درجات = 20 درجة
- الأنشطة العملية: فترتان × 10 درجات = 20 درجة  
- المشروع: 10 درجات
- **المجموع الكلي: 50 درجة**

#### الصفوف 5-10
- الأعمال الشفوية: فترتان × 10 درجات = 20 درجة
- الأنشطة العملية: فترتان × 20 درجة = 40 درجة
- المشروع: 20 درجة
- الاختبار القصير: 20 درجة
- **المجموع الكلي: 100 درجة**

#### الصفوف 11-12
- الأعمال الشفوية: فترتان × 5 درجات = 10 درجات
- الأنشطة العملية: فترتان × 20 درجة = 40 درجة
- المشروع: 20 درجة
- الاختبار القصير: 20 درجة
- الاختبار النهائي: 30 درجة
- **المجموع الكلي: 100 درجة**

### المستويات والعبارات الوصفية

#### للصفوف 1-4
- **أ**: 47.25 فأكثر (ممتاز)
- **ب**: 44.75 - 47.24 (جيد جداً)
- **ج**: 39.75 - 44.74 (جيد)
- **د**: 34.75 - 39.74 (مقبول)
- **هـ**: 29.75 - 34.74 (ضعيف)
- **و**: 24.75 - 29.74 (ضعيف جداً)
- **ز**: أقل من 24.75 (راسب)

#### للصفوف 5-12
- **أ**: 90 فأكثر (ممتاز)
- **ب**: 80-89 (جيد جداً)
- **ج**: 70-79 (جيد)
- **د**: 60-69 (مقبول)
- **هـ**: 50-59 (ضعيف)
- **راسب**: أقل من 50

## النسخ الاحتياطي والاستيراد

### إنشاء نسخة احتياطية
- من قائمة "ملف" → "نسخة احتياطية"
- أو استخدم الاختصار `Ctrl+B`
- اختر مكان الحفظ

### استيراد نسخة احتياطية
- من قائمة "ملف" → "استيراد نسخة احتياطية"
- اختر ملف النسخة الاحتياطية
- سيتم إعادة تحميل التطبيق تلقائياً

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **لا تظهر البيانات**
   - تأكد من وجود اتصال بالإنترنت (للمكتبات الخارجية)
   - امسح ذاكرة التخزين المؤقت للمتصفح
   - أعد تحميل الصفحة

2. **خطأ في حفظ البيانات**
   - تأكد من صلاحيات الكتابة في مجلد التطبيق
   - تحقق من وجود مساحة كافية على القرص

3. **مشاكل في استيراد Excel**
   - تأكد من تنسيق الملف (.xlsx أو .xls)
   - تحقق من وجود البيانات في الأعمدة الصحيحة
   - استخدم النموذج المتوفر

## المساهمة في التطوير

نرحب بمساهماتكم في تطوير التطبيق:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم والمساعدة

- **الإبلاغ عن الأخطاء**: [GitHub Issues](https://github.com/your-username/it-evaluation-system/issues)
- **طلب ميزات جديدة**: [GitHub Discussions](https://github.com/your-username/it-evaluation-system/discussions)
- **التوثيق**: [Wiki](https://github.com/your-username/it-evaluation-system/wiki)

## شكر وتقدير

- [Electron](https://electronjs.org/) - إطار عمل التطبيقات المكتبية
- [Chart.js](https://chartjs.org/) - مكتبة الرسوم البيانية
- [SheetJS](https://sheetjs.com/) - معالجة ملفات Excel
- [Font Awesome](https://fontawesome.com/) - الأيقونات
- [Google Fonts](https://fonts.google.com/) - خط Cairo العربي

---

**تم التطوير بـ ❤️ لخدمة التعليم في الوطن العربي**
