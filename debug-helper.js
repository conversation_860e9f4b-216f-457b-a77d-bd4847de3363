// Debug Helper - أداة تصحيح الأخطاء السريعة
// يمكن تشغيل هذا الملف في وحدة تحكم المطور لحل المشاكل الشائعة

console.log('🔧 Debug Helper loaded - مساعد التصحيح محمّل');

// وظيفة لتصحيح مشكلة عدم ظهور المجموع والمستوى
function fixGradeDisplay() {
    console.log('🔄 Fixing grade display issues...');
    
    // التحقق من وجود جدول الدرجات
    const gradesTable = document.querySelector('.grades-table');
    if (!gradesTable) {
        console.log('❌ No grades table found. Please navigate to Grades section first.');
        return false;
    }
    
    // الحصول على جميع صفوف الطلاب
    const studentRows = document.querySelectorAll('.grades-table tbody tr[data-student-id]');
    console.log(`📊 Found ${studentRows.length} student rows`);
    
    if (studentRows.length === 0) {
        console.log('❌ No student rows found. Please select school, grade, and section first.');
        return false;
    }
    
    let fixedCount = 0;
    
    studentRows.forEach((row, index) => {
        const studentId = row.dataset.studentId;
        console.log(`🔄 Processing student ${index + 1}/${studentRows.length} (ID: ${studentId})`);
        
        // الحصول على جميع حقول الدرجات
        const gradeInputs = row.querySelectorAll('.grade-input');
        const grades = {};
        
        gradeInputs.forEach(input => {
            const gradeType = input.dataset.gradeType;
            const value = input.value.trim();
            if (value !== '') {
                grades[gradeType] = parseFloat(value);
            }
        });
        
        // الحصول على مستوى الصف
        const gradeLevel = window.gradesManager ? window.gradesManager.currentGradeLevel : null;
        if (!gradeLevel) {
            console.log('❌ Grade level not found');
            return;
        }
        
        // حساب الدرجات
        const calculated = Utils.calculateGrades(gradeLevel, grades);
        console.log(`📊 Student ${studentId}: Total=${calculated.total}, Level=${calculated.level}, Description=${calculated.description}`);
        
        // تحديث العرض
        const totalCell = row.querySelector('.total-score');
        const levelCell = row.querySelector('.grade-level');
        const descriptionCell = row.querySelector('.grade-description');
        
        if (totalCell) {
            totalCell.textContent = calculated.total;
            console.log(`✅ Updated total for student ${studentId}: ${calculated.total}`);
        }
        
        if (levelCell) {
            levelCell.textContent = calculated.level;
            levelCell.setAttribute('data-level', calculated.level);
            console.log(`✅ Updated level for student ${studentId}: ${calculated.level}`);
        }
        
        if (descriptionCell) {
            descriptionCell.textContent = calculated.description;
            console.log(`✅ Updated description for student ${studentId}: ${calculated.description}`);
        }
        
        fixedCount++;
    });
    
    console.log(`✅ Fixed grade display for ${fixedCount} students`);
    return true;
}

// وظيفة لتصحيح ألوان المستويات
function fixGradeLevelColors() {
    console.log('🎨 Fixing grade level colors...');
    
    const levelCells = document.querySelectorAll('.grade-level');
    let coloredCount = 0;
    
    levelCells.forEach(cell => {
        const level = cell.textContent.trim();
        if (level && level !== '-') {
            cell.setAttribute('data-level', level);
            coloredCount++;
            console.log(`🎨 Applied color to level: ${level}`);
        }
    });
    
    console.log(`✅ Applied colors to ${coloredCount} grade levels`);
    return coloredCount;
}

// وظيفة لاختبار حسابات الدرجات
function testGradeCalculations() {
    console.log('🧪 Testing grade calculations...');
    
    // اختبار الصفوف 1-4
    const test1to4 = Utils.calculateGrades(4, {
        oral1: 9, oral2: 8.5, practical1: 9.5, practical2: 9, project: 9
    });
    console.log('📊 Test Grades 1-4:', test1to4);
    
    // اختبار الصفوف 5-10
    const test5to10 = Utils.calculateGrades(8, {
        oral1: 9, oral2: 9.5, practical1: 18, practical2: 19, project: 18, shortTest: 17
    });
    console.log('📊 Test Grades 5-10:', test5to10);
    
    // اختبار الصفوف 11-12
    const test11to12 = Utils.calculateGrades(11, {
        oral1: 4.5, oral2: 5, practical1: 19, practical2: 18, project: 18, shortTest: 17, finalExam: 26
    });
    console.log('📊 Test Grades 11-12:', test11to12);
    
    // اختبار حالة فارغة
    const testEmpty = Utils.calculateGrades(8, {});
    console.log('📊 Test Empty Grades:', testEmpty);
    
    console.log('✅ Grade calculation tests completed');
}

// وظيفة لتحديث جميع البيانات
function refreshAllData() {
    console.log('🔄 Refreshing all data...');
    
    if (window.app && window.app.refreshAllData) {
        window.app.refreshAllData();
        console.log('✅ App data refreshed');
    } else {
        console.log('❌ App refresh function not available');
    }
}

// وظيفة لتصحيح مشاكل التخزين المحلي
function fixLocalStorage() {
    console.log('💾 Checking local storage...');
    
    const keys = ['schools', 'students', 'grades', 'settings'];
    keys.forEach(key => {
        const data = localStorage.getItem(key);
        if (data) {
            try {
                const parsed = JSON.parse(data);
                console.log(`✅ ${key}: ${Array.isArray(parsed) ? parsed.length + ' items' : 'object'}`);
            } catch (error) {
                console.log(`❌ ${key}: Invalid JSON data`);
                localStorage.removeItem(key);
                console.log(`🗑️ Removed corrupted ${key} data`);
            }
        } else {
            console.log(`⚠️ ${key}: No data found`);
        }
    });
}

// وظيفة لعرض معلومات التصحيح
function showDebugInfo() {
    console.log('ℹ️ Debug Information:');
    console.log('📱 User Agent:', navigator.userAgent);
    console.log('🌐 URL:', window.location.href);
    console.log('📏 Screen:', screen.width + 'x' + screen.height);
    console.log('🖼️ Viewport:', window.innerWidth + 'x' + window.innerHeight);
    
    // التحقق من المتغيرات العامة
    const globals = ['app', 'db', 'Utils', 'modalManager', 'schoolsManager', 'studentsManager', 'gradesManager'];
    globals.forEach(name => {
        console.log(`🔧 ${name}:`, typeof window[name] !== 'undefined' ? '✅ Available' : '❌ Not found');
    });
    
    // التحقق من المكتبات الخارجية
    const libraries = ['Chart', 'XLSX'];
    libraries.forEach(name => {
        console.log(`📚 ${name}:`, typeof window[name] !== 'undefined' ? '✅ Loaded' : '❌ Not loaded');
    });
}

// وظيفة شاملة لحل جميع المشاكل
function fixAllIssues() {
    console.log('🔧 Running comprehensive fix...');
    
    showDebugInfo();
    fixLocalStorage();
    testGradeCalculations();
    
    // انتظار قليل ثم تصحيح العرض
    setTimeout(() => {
        fixGradeDisplay();
        fixGradeLevelColors();
        console.log('✅ All fixes completed!');
    }, 1000);
}

// إضافة الوظائف للنافذة العامة
window.debugHelper = {
    fixGradeDisplay,
    fixGradeLevelColors,
    testGradeCalculations,
    refreshAllData,
    fixLocalStorage,
    showDebugInfo,
    fixAllIssues
};

// عرض تعليمات الاستخدام
console.log(`
🔧 Debug Helper Commands:
- debugHelper.fixGradeDisplay() - إصلاح عرض الدرجات
- debugHelper.fixGradeLevelColors() - إصلاح ألوان المستويات  
- debugHelper.testGradeCalculations() - اختبار حسابات الدرجات
- debugHelper.refreshAllData() - تحديث جميع البيانات
- debugHelper.fixLocalStorage() - إصلاح التخزين المحلي
- debugHelper.showDebugInfo() - عرض معلومات التصحيح
- debugHelper.fixAllIssues() - إصلاح شامل لجميع المشاكل

Quick fix: debugHelper.fixAllIssues()
`);

// تشغيل تلقائي للإصلاح إذا كان هناك مشاكل واضحة
setTimeout(() => {
    const gradesTable = document.querySelector('.grades-table');
    if (gradesTable) {
        const emptyTotals = document.querySelectorAll('.total-score:empty, .grade-level:empty');
        if (emptyTotals.length > 0) {
            console.log('🔄 Auto-fixing detected issues...');
            fixGradeDisplay();
            fixGradeLevelColors();
        }
    }
}, 2000);
