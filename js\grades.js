// Grades management system

class GradesManager {
    constructor() {
        this.students = [];
        this.schools = [];
        this.grades = [];
        this.currentGradeLevel = null;
        this.currentSchool = null;
        this.currentSection = null;
        this.currentSemester = 1;
        this.currentAcademicYear = '2024-2025';
        this.gradeStructure = {};
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Filter event listeners
        const schoolFilter = document.getElementById('gradesSchoolFilter');
        if (schoolFilter) {
            schoolFilter.addEventListener('change', (e) => {
                this.currentSchool = e.target.value;
                this.loadGradeFilter();
                this.clearGradesTable();
            });
        }

        const gradeFilter = document.getElementById('gradesGradeFilter');
        if (gradeFilter) {
            gradeFilter.addEventListener('change', (e) => {
                this.currentGradeLevel = parseInt(e.target.value);
                this.loadSectionFilter();
                this.updateGradeStructure();
                this.clearGradesTable();
            });
        }

        const sectionFilter = document.getElementById('gradesSectionFilter');
        if (sectionFilter) {
            sectionFilter.addEventListener('change', (e) => {
                this.currentSection = e.target.value;
                this.loadGradesTable();
            });
        }

        const semesterFilter = document.getElementById('semesterFilter');
        if (semesterFilter) {
            semesterFilter.addEventListener('change', (e) => {
                this.currentSemester = parseInt(e.target.value);
                this.loadGradesTable();
            });
        }

        const academicYearFilter = document.getElementById('academicYearFilter');
        if (academicYearFilter) {
            academicYearFilter.addEventListener('change', (e) => {
                this.currentAcademicYear = e.target.value;
                this.loadGradesTable();
            });
        }
    }

    async initialize() {
        try {
            [this.students, this.schools] = await Promise.all([
                db.getStudents(),
                db.getSchools()
            ]);
            
            this.loadSchoolsFilter();
        } catch (error) {
            console.error('Error initializing grades manager:', error);
            Utils.showNotification('خطأ في تحميل البيانات', 'error');
        }
    }

    loadSchoolsFilter() {
        const schoolFilter = document.getElementById('gradesSchoolFilter');
        if (!schoolFilter) return;

        schoolFilter.innerHTML = '<option value="">اختر المدرسة</option>';
        this.schools.forEach(school => {
            const option = document.createElement('option');
            option.value = school.id;
            option.textContent = school.name;
            schoolFilter.appendChild(option);
        });
    }

    loadGradeFilter() {
        const gradeFilter = document.getElementById('gradesGradeFilter');
        if (!gradeFilter || !this.currentSchool) return;

        // Get unique grades for the selected school
        const schoolStudents = this.students.filter(s => s.schoolId === this.currentSchool);
        const grades = [...new Set(schoolStudents.map(s => s.grade))].sort((a, b) => a - b);

        gradeFilter.innerHTML = '<option value="">اختر الصف</option>';
        grades.forEach(grade => {
            const option = document.createElement('option');
            option.value = grade;
            option.textContent = this.getGradeName(grade);
            gradeFilter.appendChild(option);
        });
    }

    loadSectionFilter() {
        const sectionFilter = document.getElementById('gradesSectionFilter');
        if (!sectionFilter || !this.currentSchool || !this.currentGradeLevel) return;

        // Get unique sections for the selected school and grade
        const schoolGradeStudents = this.students.filter(s => 
            s.schoolId === this.currentSchool && s.grade === this.currentGradeLevel
        );
        const sections = [...new Set(schoolGradeStudents.map(s => s.section))].sort();

        sectionFilter.innerHTML = '<option value="">اختر الشعبة</option>';
        sections.forEach(section => {
            const option = document.createElement('option');
            option.value = section;
            option.textContent = section;
            sectionFilter.appendChild(option);
        });
    }

    updateGradeStructure() {
        if (!this.currentGradeLevel) return;
        this.gradeStructure = Utils.getGradeStructure(this.currentGradeLevel);
    }

    async loadGradesTable() {
        if (!this.currentSchool || !this.currentGradeLevel || !this.currentSection) {
            this.clearGradesTable();
            return;
        }

        const container = document.getElementById('gradesContainer');
        if (!container) return;

        try {
            const loading = Utils.showLoading(container);

            // Get students for the selected criteria
            const students = this.students.filter(s => 
                s.schoolId === this.currentSchool && 
                s.grade === this.currentGradeLevel && 
                s.section === this.currentSection
            );

            if (students.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-users fa-3x"></i>
                        <h3>لا توجد طلاب</h3>
                        <p>لا توجد طلاب في هذه الشعبة</p>
                    </div>
                `;
                return;
            }

            // Load existing grades
            this.grades = await db.getGrades();
            
            // Render grades table
            this.renderGradesTable(students);
            
            Utils.hideLoading(loading);
        } catch (error) {
            console.error('Error loading grades table:', error);
            Utils.showNotification('خطأ في تحميل جدول الدرجات', 'error');
        }
    }

    renderGradesTable(students) {
        const container = document.getElementById('gradesContainer');
        if (!container) return;

        const structure = this.gradeStructure;
        const structureKeys = Object.keys(structure).filter(key => key !== 'total');

        let tableHTML = `
            <div class="grades-table-container">
                <div class="grades-header">
                    <h3>درجات ${this.getGradeName(this.currentGradeLevel)} - شعبة ${this.currentSection}</h3>
                    <div class="grades-info">
                        <span>الفصل الدراسي: ${this.currentSemester === 1 ? 'الأول' : 'الثاني'}</span>
                        <span>العام الدراسي: ${this.currentAcademicYear}</span>
                        <span>المجموع الكلي: ${structure.total}</span>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table grades-table">
                        <thead>
                            <tr>
                                <th rowspan="2">اسم الطالب</th>
                                <th rowspan="2">رقم الطالب</th>
                                ${structureKeys.map(key => `<th>${structure[key].label}</th>`).join('')}
                                <th rowspan="2">المجموع</th>
                                <th rowspan="2">المستوى</th>
                                <th rowspan="2">العبارة الوصفية</th>
                                <th rowspan="2">الإجراءات</th>
                            </tr>
                            <tr>
                                ${structureKeys.map(key => `<th class="max-score">(${structure[key].max})</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${students.map(student => this.renderStudentGradeRow(student, structureKeys)).join('')}
                        </tbody>
                    </table>
                </div>
                <div class="grades-actions">
                    <button class="btn btn-primary" onclick="gradesManager.saveAllGrades()">
                        <i class="fas fa-save"></i> حفظ جميع الدرجات
                    </button>
                    <button class="btn btn-secondary" onclick="gradesManager.exportGrades()">
                        <i class="fas fa-download"></i> تصدير الدرجات
                    </button>
                    <button class="btn btn-success" onclick="gradesManager.calculateStatistics()">
                        <i class="fas fa-chart-bar"></i> عرض الإحصائيات
                    </button>
                </div>
            </div>
        `;

        container.innerHTML = tableHTML;
        this.addGradeInputListeners();
    }

    renderStudentGradeRow(student, structureKeys) {
        // Get existing grades for this student
        const existingGrade = this.grades.find(g => 
            g.studentId === student.id && 
            g.schoolId === this.currentSchool && 
            g.grade === this.currentGradeLevel && 
            g.section === this.currentSection && 
            g.semester === this.currentSemester && 
            g.academicYear === this.currentAcademicYear
        );

        const grades = existingGrade ? existingGrade.grades : {};
        const calculated = Utils.calculateGrades(this.currentGradeLevel, grades);

        return `
            <tr data-student-id="${student.id}">
                <td class="student-name">${student.name}</td>
                <td class="student-id">${student.studentId}</td>
                ${structureKeys.map(key => `
                    <td>
                        <input type="number" 
                               class="grade-input" 
                               data-student-id="${student.id}" 
                               data-grade-type="${key}"
                               value="${grades[key] || ''}" 
                               min="0" 
                               max="${this.gradeStructure[key].max}"
                               step="0.5">
                    </td>
                `).join('')}
                <td class="total-score" data-student-id="${student.id}">${calculated.total}</td>
                <td class="grade-level" data-student-id="${student.id}">${calculated.level}</td>
                <td class="grade-description" data-student-id="${student.id}">${calculated.description}</td>
                <td class="action-buttons">
                    <button class="btn-icon save-student-grade" data-student-id="${student.id}" title="حفظ">
                        <i class="fas fa-save"></i>
                    </button>
                    <button class="btn-icon clear-student-grade" data-student-id="${student.id}" title="مسح">
                        <i class="fas fa-eraser"></i>
                    </button>
                </td>
            </tr>
        `;
    }

    addGradeInputListeners() {
        // Grade input change listeners
        document.querySelectorAll('.grade-input').forEach(input => {
            input.addEventListener('input', (e) => {
                this.updateStudentCalculations(e.target.dataset.studentId);
            });

            input.addEventListener('blur', (e) => {
                this.validateGradeInput(e.target);
            });
        });

        // Save individual student grade buttons
        document.querySelectorAll('.save-student-grade').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.saveStudentGrade(e.target.dataset.studentId);
            });
        });

        // Clear individual student grade buttons
        document.querySelectorAll('.clear-student-grade').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.clearStudentGrade(e.target.dataset.studentId);
            });
        });
    }

    updateStudentCalculations(studentId) {
        const row = document.querySelector(`tr[data-student-id="${studentId}"]`);
        if (!row) return;

        // Get all grade inputs for this student
        const gradeInputs = row.querySelectorAll('.grade-input');
        const grades = {};

        gradeInputs.forEach(input => {
            const gradeType = input.dataset.gradeType;
            const value = parseFloat(input.value);
            if (!isNaN(value)) {
                grades[gradeType] = value;
            }
        });

        // Calculate totals
        const calculated = Utils.calculateGrades(this.currentGradeLevel, grades);

        // Update display
        const totalCell = row.querySelector('.total-score');
        const levelCell = row.querySelector('.grade-level');
        const descriptionCell = row.querySelector('.grade-description');

        if (totalCell) totalCell.textContent = calculated.total;
        if (levelCell) levelCell.textContent = calculated.level;
        if (descriptionCell) descriptionCell.textContent = calculated.description;
    }

    validateGradeInput(input) {
        const value = parseFloat(input.value);
        const max = parseFloat(input.max);
        const min = parseFloat(input.min);

        if (input.value !== '' && (isNaN(value) || value < min || value > max)) {
            input.classList.add('error');
            Utils.showNotification(`الدرجة يجب أن تكون بين ${min} و ${max}`, 'error');
        } else {
            input.classList.remove('error');
        }
    }

    async saveStudentGrade(studentId) {
        try {
            const row = document.querySelector(`tr[data-student-id="${studentId}"]`);
            if (!row) return;

            // Get all grade inputs for this student
            const gradeInputs = row.querySelectorAll('.grade-input');
            const grades = {};
            let hasErrors = false;

            gradeInputs.forEach(input => {
                if (input.classList.contains('error')) {
                    hasErrors = true;
                    return;
                }

                const gradeType = input.dataset.gradeType;
                const value = input.value.trim();
                if (value !== '') {
                    grades[gradeType] = parseFloat(value);
                }
            });

            if (hasErrors) {
                Utils.showNotification('يرجى تصحيح الأخطاء قبل الحفظ', 'error');
                return;
            }

            // Save to database
            await db.saveStudentGrades(
                studentId,
                this.currentSchool,
                this.currentGradeLevel,
                this.currentSection,
                this.currentSemester,
                this.currentAcademicYear,
                grades
            );

            Utils.showNotification('تم حفظ درجات الطالب بنجاح', 'success');
            
            // Update the row styling to indicate saved
            row.classList.add('saved');
            setTimeout(() => row.classList.remove('saved'), 2000);

        } catch (error) {
            console.error('Error saving student grade:', error);
            Utils.showNotification('خطأ في حفظ الدرجات', 'error');
        }
    }

    clearStudentGrade(studentId) {
        if (confirm('هل أنت متأكد من مسح جميع درجات هذا الطالب؟')) {
            const row = document.querySelector(`tr[data-student-id="${studentId}"]`);
            if (!row) return;

            // Clear all inputs
            const gradeInputs = row.querySelectorAll('.grade-input');
            gradeInputs.forEach(input => {
                input.value = '';
                input.classList.remove('error');
            });

            // Update calculations
            this.updateStudentCalculations(studentId);
        }
    }

    async saveAllGrades() {
        try {
            const rows = document.querySelectorAll('.grades-table tbody tr');
            let savedCount = 0;
            let errorCount = 0;

            for (const row of rows) {
                const studentId = row.dataset.studentId;
                
                try {
                    await this.saveStudentGrade(studentId);
                    savedCount++;
                } catch (error) {
                    errorCount++;
                }
            }

            if (errorCount === 0) {
                Utils.showNotification(`تم حفظ درجات ${savedCount} طالب بنجاح`, 'success');
            } else {
                Utils.showNotification(`تم حفظ ${savedCount} طالب، فشل في حفظ ${errorCount} طالب`, 'warning');
            }

        } catch (error) {
            console.error('Error saving all grades:', error);
            Utils.showNotification('خطأ في حفظ الدرجات', 'error');
        }
    }

    async exportGrades() {
        try {
            const students = this.students.filter(s => 
                s.schoolId === this.currentSchool && 
                s.grade === this.currentGradeLevel && 
                s.section === this.currentSection
            );

            const exportData = [];
            const structure = this.gradeStructure;
            const structureKeys = Object.keys(structure).filter(key => key !== 'total');

            for (const student of students) {
                const existingGrade = this.grades.find(g => 
                    g.studentId === student.id && 
                    g.schoolId === this.currentSchool && 
                    g.grade === this.currentGradeLevel && 
                    g.section === this.currentSection && 
                    g.semester === this.currentSemester && 
                    g.academicYear === this.currentAcademicYear
                );

                const grades = existingGrade ? existingGrade.grades : {};
                const calculated = Utils.calculateGrades(this.currentGradeLevel, grades);

                const rowData = {
                    'اسم الطالب': student.name,
                    'رقم الطالب': student.studentId
                };

                structureKeys.forEach(key => {
                    rowData[structure[key].label] = grades[key] || '';
                });

                rowData['المجموع'] = calculated.total;
                rowData['المستوى'] = calculated.level;
                rowData['العبارة الوصفية'] = calculated.description;

                exportData.push(rowData);
            }

            const school = this.schools.find(s => s.id === this.currentSchool);
            const filename = `درجات_${school?.name || 'مدرسة'}_${this.getGradeName(this.currentGradeLevel)}_${this.currentSection}_${this.currentAcademicYear}_ف${this.currentSemester}.xlsx`;
            
            Utils.exportToExcel(exportData, filename);
            Utils.showNotification('تم تصدير الدرجات بنجاح', 'success');

        } catch (error) {
            console.error('Error exporting grades:', error);
            Utils.showNotification('خطأ في تصدير الدرجات', 'error');
        }
    }

    async calculateStatistics() {
        try {
            const students = this.students.filter(s => 
                s.schoolId === this.currentSchool && 
                s.grade === this.currentGradeLevel && 
                s.section === this.currentSection
            );

            const gradesData = [];

            for (const student of students) {
                const existingGrade = this.grades.find(g => 
                    g.studentId === student.id && 
                    g.schoolId === this.currentSchool && 
                    g.grade === this.currentGradeLevel && 
                    g.section === this.currentSection && 
                    g.semester === this.currentSemester && 
                    g.academicYear === this.currentAcademicYear
                );

                if (existingGrade) {
                    const calculated = Utils.calculateGrades(this.currentGradeLevel, existingGrade.grades);
                    gradesData.push(calculated);
                }
            }

            const statistics = Utils.calculateStatistics(gradesData);
            this.showStatisticsModal(statistics);

        } catch (error) {
            console.error('Error calculating statistics:', error);
            Utils.showNotification('خطأ في حساب الإحصائيات', 'error');
        }
    }

    showStatisticsModal(statistics) {
        const modalContent = `
            <div class="modal-header">
                <h3 class="modal-title">إحصائيات الدرجات</h3>
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="statistics-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h4>إجمالي الطلاب</h4>
                        <span class="stat-number">${statistics.total}</span>
                    </div>
                    <div class="stat-card">
                        <h4>الناجحون</h4>
                        <span class="stat-number">${statistics.passed}</span>
                    </div>
                    <div class="stat-card">
                        <h4>الراسبون</h4>
                        <span class="stat-number">${statistics.failed}</span>
                    </div>
                    <div class="stat-card">
                        <h4>نسبة النجاح</h4>
                        <span class="stat-number">${statistics.passRate}%</span>
                    </div>
                    <div class="stat-card">
                        <h4>المتوسط العام</h4>
                        <span class="stat-number">${statistics.average}</span>
                    </div>
                </div>
                <div class="distribution-chart">
                    <h4>توزيع المستويات</h4>
                    <div class="distribution-bars">
                        ${Object.entries(statistics.distribution).map(([level, count]) => `
                            <div class="distribution-bar">
                                <span class="level-label">${level}</span>
                                <div class="bar-container">
                                    <div class="bar" style="width: ${statistics.total > 0 ? (count / statistics.total) * 100 : 0}%"></div>
                                </div>
                                <span class="count-label">${count}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        modalManager.openModal(modalContent);
    }

    clearGradesTable() {
        const container = document.getElementById('gradesContainer');
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-chart-line fa-3x"></i>
                    <h3>اختر المعايير</h3>
                    <p>اختر المدرسة والصف والشعبة لعرض جدول الدرجات</p>
                </div>
            `;
        }
    }

    getGradeName(grade) {
        const names = {
            1: 'الأول', 2: 'الثاني', 3: 'الثالث', 4: 'الرابع',
            5: 'الخامس', 6: 'السادس', 7: 'السابع', 8: 'الثامن',
            9: 'التاسع', 10: 'العاشر', 11: 'الحادي عشر', 12: 'الثاني عشر'
        };
        return names[grade] || grade.toString();
    }

    selectStudent(studentId) {
        const student = this.students.find(s => s.id === studentId);
        if (!student) return;

        // Set filters to show this student's class
        document.getElementById('gradesSchoolFilter').value = student.schoolId;
        document.getElementById('gradesGradeFilter').value = student.grade;
        
        this.currentSchool = student.schoolId;
        this.currentGradeLevel = student.grade;
        
        this.loadGradeFilter();
        this.loadSectionFilter();
        this.updateGradeStructure();
        
        setTimeout(() => {
            document.getElementById('gradesSectionFilter').value = student.section;
            this.currentSection = student.section;
            this.loadGradesTable();
        }, 100);
    }
}

// Create global grades manager instance
const gradesManager = new GradesManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize grades manager when grades section is first shown
    const gradesSection = document.getElementById('grades');
    if (gradesSection) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (gradesSection.classList.contains('active') && !gradesManager.initialized) {
                        gradesManager.initialize();
                        gradesManager.initialized = true;
                    }
                }
            });
        });
        
        observer.observe(gradesSection, { attributes: true });
    }
});

// Make gradesManager available globally
window.gradesManager = gradesManager;
