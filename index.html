<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استمارة تقويم تقنية المعلومات</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/rtl.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <i class="fas fa-graduation-cap"></i>
                        <h1>استمارة تقويم تقنية المعلومات</h1>
                    </div>
                    <nav class="nav">
                        <button class="nav-btn active" data-section="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            لوحة التحكم
                        </button>
                        <button class="nav-btn" data-section="schools">
                            <i class="fas fa-school"></i>
                            المدارس
                        </button>
                        <button class="nav-btn" data-section="students">
                            <i class="fas fa-users"></i>
                            الطلاب
                        </button>
                        <button class="nav-btn" data-section="grades">
                            <i class="fas fa-chart-line"></i>
                            الدرجات
                        </button>
                        <button class="nav-btn" data-section="reports">
                            <i class="fas fa-file-alt"></i>
                            التقارير
                        </button>
                        <button class="nav-btn" data-section="statistics">
                            <i class="fas fa-chart-bar"></i>
                            الإحصائيات
                        </button>
                        <button class="nav-btn" data-section="settings">
                            <i class="fas fa-cog"></i>
                            الإعدادات
                        </button>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            <div class="container">
                <!-- Dashboard Section -->
                <section id="dashboard" class="section active">
                    <div class="section-header">
                        <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>
                    </div>
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-icon">
                                <i class="fas fa-school"></i>
                            </div>
                            <div class="card-content">
                                <h3>المدارس المسجلة</h3>
                                <span class="card-number" id="schoolsCount">0</span>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3>إجمالي الطلاب</h3>
                                <span class="card-number" id="studentsCount">0</span>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <h3>الدرجات المدخلة</h3>
                                <span class="card-number" id="gradesCount">0</span>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <div class="card-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="card-content">
                                <h3>العام الدراسي الحالي</h3>
                                <span class="card-text" id="currentYear">2024-2025</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Schools Section -->
                <section id="schools" class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-school"></i> إدارة المدارس</h2>
                        <button class="btn btn-primary" id="addSchoolBtn">
                            <i class="fas fa-plus"></i> إضافة مدرسة
                        </button>
                    </div>
                    <div class="schools-container">
                        <div class="schools-list" id="schoolsList">
                            <!-- Schools will be loaded here -->
                        </div>
                    </div>
                </section>

                <!-- Students Section -->
                <section id="students" class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-users"></i> إدارة الطلاب</h2>
                        <div class="section-actions">
                            <button class="btn btn-primary" id="addStudentBtn">
                                <i class="fas fa-user-plus"></i> إضافة طالب
                            </button>
                            <button class="btn btn-secondary" id="importStudentsBtn">
                                <i class="fas fa-file-excel"></i> استيراد من Excel
                            </button>
                        </div>
                    </div>
                    <div class="filters">
                        <select id="schoolFilter" class="form-select">
                            <option value="">جميع المدارس</option>
                        </select>
                        <select id="gradeFilter" class="form-select">
                            <option value="">جميع الصفوف</option>
                            <option value="1">الصف الأول</option>
                            <option value="2">الصف الثاني</option>
                            <option value="3">الصف الثالث</option>
                            <option value="4">الصف الرابع</option>
                            <option value="5">الصف الخامس</option>
                            <option value="6">الصف السادس</option>
                            <option value="7">الصف السابع</option>
                            <option value="8">الصف الثامن</option>
                            <option value="9">الصف التاسع</option>
                            <option value="10">الصف العاشر</option>
                            <option value="11">الصف الحادي عشر</option>
                            <option value="12">الصف الثاني عشر</option>
                        </select>
                        <input type="text" id="searchStudents" class="form-input" placeholder="البحث عن طالب...">
                    </div>
                    <div class="students-container">
                        <div class="students-table" id="studentsTable">
                            <!-- Students table will be loaded here -->
                        </div>
                    </div>
                </section>

                <!-- Grades Section -->
                <section id="grades" class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-line"></i> إدخال الدرجات</h2>
                    </div>
                    <div class="grades-filters">
                        <select id="gradesSchoolFilter" class="form-select">
                            <option value="">اختر المدرسة</option>
                        </select>
                        <select id="gradesGradeFilter" class="form-select">
                            <option value="">اختر الصف</option>
                        </select>
                        <select id="gradesSectionFilter" class="form-select">
                            <option value="">اختر الشعبة</option>
                        </select>
                        <select id="semesterFilter" class="form-select">
                            <option value="1">الفصل الأول</option>
                            <option value="2">الفصل الثاني</option>
                        </select>
                        <select id="academicYearFilter" class="form-select">
                            <option value="2024-2025">2024-2025</option>
                            <option value="2023-2024">2023-2024</option>
                        </select>
                    </div>
                    <div class="grades-container" id="gradesContainer">
                        <!-- Grades input table will be loaded here -->
                    </div>
                </section>

                <!-- Reports Section -->
                <section id="reports" class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-file-alt"></i> التقارير</h2>
                    </div>
                    <div class="reports-container">
                        <!-- Reports will be loaded here -->
                    </div>
                </section>

                <!-- Statistics Section -->
                <section id="statistics" class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-bar"></i> الإحصائيات والتحليلات</h2>
                    </div>
                    <div class="statistics-container">
                        <!-- Statistics and charts will be loaded here -->
                    </div>
                </section>

                <!-- Settings Section -->
                <section id="settings" class="section">
                    <div class="section-header">
                        <h2><i class="fas fa-cog"></i> إعدادات النظام</h2>
                        <div class="section-actions">
                            <button class="btn btn-success" id="saveSettingsBtn">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                            <button class="btn btn-secondary" id="resetSettingsBtn">
                                <i class="fas fa-undo"></i> استعادة الافتراضي
                            </button>
                        </div>
                    </div>
                    <div class="settings-container">
                        <!-- Settings will be loaded here -->
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="modalOverlay" class="modal-overlay">
        <div id="modalContent" class="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="js/database.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/modals.js"></script>
    <script src="js/schools.js"></script>
    <script src="js/students.js"></script>
    <script src="js/grades.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/statistics.js"></script>
    <script src="js/settings.js"></script>
    <script src="js/app.js"></script>
    <script src="sample-data/load-sample-data.js"></script>
    <script src="debug-helper.js"></script>
    <script src="settings-quick-test.js"></script>
    <script src="test-student-id.js"></script>
    <script src="test-staff-info.js"></script>
</body>
</html>
