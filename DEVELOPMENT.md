# دليل التطوير - استمارة تقويم تقنية المعلومات

## هيكل المشروع

```
it-evaluation-system/
├── index.html                 # الصفحة الرئيسية
├── package.json              # إعدادات Node.js و Electron
├── README.md                 # دليل المستخدم
├── QUICK_START.md           # دليل البدء السريع
├── DEVELOPMENT.md           # دليل التطوير (هذا الملف)
├── .gitignore               # ملفات Git المستبعدة
│
├── css/                     # ملفات الأنماط
│   ├── styles.css          # الأنماط الأساسية
│   └── rtl.css             # دعم RTL للعربية
│
├── js/                      # ملفات JavaScript
│   ├── app.js              # التطبيق الرئيسي
│   ├── database.js         # إدارة قاعدة البيانات
│   ├── utils.js            # الوظائف المساعدة
│   ├── modals.js           # إدارة النوافذ المنبثقة
│   ├── schools.js          # إدارة المدارس
│   ├── students.js         # إدارة الطلاب
│   ├── grades.js           # إدارة الدرجات
│   ├── reports.js          # نظام التقارير
│   └── statistics.js       # الإحصائيات والتحليلات
│
├── electron/                # ملفات Electron
│   └── main.js             # العملية الرئيسية لـ Electron
│
├── sample-data/             # البيانات التجريبية
│   ├── sample-schools.json
│   ├── sample-students.json
│   ├── sample-grades.json
│   ├── sample-settings.json
│   └── load-sample-data.js
│
└── assets/                  # الأصول (أيقونات، صور)
    ├── icon.ico            # أيقونة Windows
    ├── icon.icns           # أيقونة macOS
    └── icon.png            # أيقونة Linux
```

## التقنيات المستخدمة

### Frontend
- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والأنماط
  - CSS Grid & Flexbox للتخطيط
  - CSS Variables للألوان والمتغيرات
  - Media Queries للتصميم المتجاوب
- **JavaScript (Vanilla)**: المنطق والتفاعل
  - ES6+ Features
  - Async/Await للعمليات غير المتزامنة
  - Classes للتنظيم

### Backend/Desktop
- **Node.js**: بيئة التشغيل
- **Electron**: تطبيق سطح المكتب
- **File System (fs)**: تخزين البيانات

### المكتبات الخارجية
- **Chart.js**: الرسوم البيانية
- **SheetJS (XLSX)**: معالجة ملفات Excel
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo العربي

## معمارية التطبيق

### نمط MVC المبسط
- **Model**: `database.js` - إدارة البيانات
- **View**: HTML + CSS - واجهة المستخدم
- **Controller**: ملفات JS المختلفة - منطق التطبيق

### إدارة الحالة
- البيانات تُخزن في localStorage (المتصفح) أو ملفات JSON (Electron)
- كل مدير (Manager) يحتفظ بحالته المحلية
- التحديثات تتم عبر الأحداث والاستدعاءات المباشرة

### نمط Module Pattern
كل ملف JS يحتوي على:
```javascript
class ManagerName {
    constructor() {
        // التهيئة
    }
    
    async initialize() {
        // تحميل البيانات
    }
    
    // الوظائف الأساسية
}

const managerInstance = new ManagerName();
window.managerInstance = managerInstance;
```

## إضافة ميزات جديدة

### 1. إضافة قسم جديد

#### إنشاء HTML
```html
<section id="newSection" class="section">
    <div class="section-header">
        <h2><i class="fas fa-icon"></i> اسم القسم</h2>
    </div>
    <div class="section-content">
        <!-- محتوى القسم -->
    </div>
</section>
```

#### إضافة زر التنقل
```html
<button class="nav-btn" data-section="newSection">
    <i class="fas fa-icon"></i>
    اسم القسم
</button>
```

#### إنشاء مدير JavaScript
```javascript
// js/new-section.js
class NewSectionManager {
    constructor() {
        this.data = [];
        this.initializeEventListeners();
    }
    
    async initialize() {
        await this.loadData();
        this.renderContent();
    }
    
    // باقي الوظائف
}

const newSectionManager = new NewSectionManager();
window.newSectionManager = newSectionManager;
```

### 2. إضافة نوع تقرير جديد

#### في `reports.js`
```javascript
// إضافة في generateReport()
case 'newReportType':
    reportHTML = await this.generateNewReport();
    break;

// إضافة الوظيفة
async generateNewReport() {
    // منطق التقرير الجديد
    return reportHTML;
}
```

#### في HTML
```html
<div class="report-type-card">
    <div class="report-icon">
        <i class="fas fa-new-icon"></i>
    </div>
    <h4>اسم التقرير الجديد</h4>
    <p>وصف التقرير</p>
    <button class="btn btn-primary report-type-btn" data-report-type="newReportType">
        إنشاء التقرير
    </button>
</div>
```

### 3. إضافة نوع رسم بياني جديد

#### في `statistics.js`
```javascript
updateNewChart(data) {
    const ctx = document.getElementById('newChart');
    if (!ctx) return;

    // تدمير الرسم الموجود
    if (this.charts.newChart) {
        this.charts.newChart.destroy();
    }

    // إنشاء رسم جديد
    this.charts.newChart = new Chart(ctx, {
        type: 'chartType',
        data: chartData,
        options: chartOptions
    });
}
```

## إرشادات التطوير

### 1. تسمية الملفات والمتغيرات
- **الملفات**: kebab-case (مثل: `student-grades.js`)
- **الكلاسات**: PascalCase (مثل: `StudentManager`)
- **المتغيرات**: camelCase (مثل: `studentData`)
- **الثوابت**: UPPER_CASE (مثل: `MAX_STUDENTS`)

### 2. التعليقات والتوثيق
```javascript
/**
 * وصف الوظيفة
 * @param {string} param1 - وصف المعامل الأول
 * @param {number} param2 - وصف المعامل الثاني
 * @returns {Promise<Object>} وصف القيمة المرجعة
 */
async function exampleFunction(param1, param2) {
    // تنفيذ الوظيفة
}
```

### 3. معالجة الأخطاء
```javascript
try {
    const result = await someAsyncOperation();
    return result;
} catch (error) {
    console.error('Error in operation:', error);
    Utils.showNotification('رسالة خطأ للمستخدم', 'error');
    throw error; // إعادة رمي الخطأ إذا لزم الأمر
}
```

### 4. التحقق من صحة البيانات
```javascript
// استخدام Utils.validateStudent() أو إنشاء وظائف تحقق مخصصة
const errors = Utils.validateStudent(studentData);
if (errors.length > 0) {
    Utils.showNotification(errors.join('<br>'), 'error');
    return false;
}
```

### 5. الأداء والذاكرة
- استخدم `Utils.debounce()` للبحث والفلترة
- امسح event listeners عند عدم الحاجة
- استخدم `requestAnimationFrame` للرسوم المتحركة
- تجنب تسريب الذاكرة في الرسوم البيانية

## اختبار التطبيق

### 1. اختبار يدوي
- اختبر جميع الوظائف في المتصفحات المختلفة
- اختبر التصميم المتجاوب على أحجام شاشات مختلفة
- اختبر استيراد/تصدير البيانات
- اختبر النسخ الاحتياطي والاستعادة

### 2. اختبار البيانات
```javascript
// تحميل البيانات التجريبية
loadSampleData();

// اختبار العمليات الأساسية
await db.addSchool(testSchool);
await db.addStudent(testStudent);
await db.saveStudentGrades(studentId, gradeData);
```

### 3. اختبار الأداء
```javascript
// قياس وقت التحميل
console.time('loadStudents');
await studentsManager.loadStudents();
console.timeEnd('loadStudents');

// مراقبة استخدام الذاكرة
console.log(performance.memory);
```

## بناء التطبيق للإنتاج

### 1. تحسين الكود
- تصغير ملفات CSS و JS
- ضغط الصور والأيقونات
- إزالة console.log من الإنتاج

### 2. بناء Electron
```bash
# تثبيت التبعيات
npm install

# بناء للأنظمة المختلفة
npm run build-win    # Windows
npm run build-mac    # macOS
npm run build-linux  # Linux
npm run build        # جميع الأنظمة
```

### 3. اختبار النسخة النهائية
- اختبر التطبيق المبني على أنظمة مختلفة
- تأكد من عمل جميع الوظائف
- اختبر التثبيت وإلغاء التثبيت

## إرشادات المساهمة

### 1. Git Workflow
```bash
# إنشاء فرع جديد
git checkout -b feature/new-feature

# إضافة التغييرات
git add .
git commit -m "Add new feature: description"

# دفع الفرع
git push origin feature/new-feature

# إنشاء Pull Request
```

### 2. معايير الكود
- اتبع نمط الكود الموجود
- أضف تعليقات للكود المعقد
- اختبر التغييرات قبل الإرسال
- حدث التوثيق عند الحاجة

### 3. رسائل Commit
```
feat: إضافة ميزة جديدة
fix: إصلاح خطأ
docs: تحديث التوثيق
style: تحسين التصميم
refactor: إعادة هيكلة الكود
test: إضافة اختبارات
chore: مهام صيانة
```

## الأدوات المساعدة

### 1. أدوات التطوير
- **VS Code**: محرر النصوص الموصى به
- **Live Server**: لتشغيل خادم محلي
- **Chrome DevTools**: لتصحيح الأخطاء

### 2. إضافات VS Code المفيدة
- Arabic Language Pack
- Live Server
- Prettier
- ESLint
- Auto Rename Tag

### 3. أدوات التصحيح
```javascript
// في وضع التطوير
if (process.env.NODE_ENV === 'development') {
    console.log('Debug info:', data);
}

// استخدام debugger
debugger; // توقف هنا في أدوات المطور
```

## الأمان والخصوصية

### 1. حماية البيانات
- لا تخزن كلمات مرور في النص الواضح
- تشفير البيانات الحساسة
- التحقق من صحة جميع المدخلات

### 2. أمان Electron
```javascript
// في main.js
webPreferences: {
    nodeIntegration: false,    // تعطيل في الإنتاج
    contextIsolation: true,    // تفعيل العزل
    enableRemoteModule: false  // تعطيل الوحدة البعيدة
}
```

## الدعم والصيانة

### 1. تسجيل الأخطاء
```javascript
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    // إرسال التقرير للخادم (اختياري)
});
```

### 2. التحديثات التلقائية
- استخدم electron-updater للتحديثات التلقائية
- أضف آلية للتحقق من الإصدارات الجديدة

### 3. النسخ الاحتياطي التلقائي
```javascript
// في app.js
setInterval(() => {
    if (settings.autoBackupEnabled) {
        app.createBackup();
    }
}, settings.autoBackupInterval);
```

---

**للمزيد من المساعدة، راجع الملفات الأخرى أو أنشئ Issue في GitHub**
