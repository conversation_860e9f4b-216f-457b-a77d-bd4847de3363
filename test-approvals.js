// Quick test for approvals functionality
// Run this in browser console to test approvals system

console.log('🏆 Approvals System Test');

async function testApprovalsSystem() {
    console.log('📋 Testing Approvals System...');
    
    try {
        // Test 1: Check if approvals manager exists
        if (typeof window.approvalsManager === 'undefined') {
            console.log('❌ Approvals manager not found');
            return false;
        }
        
        console.log('✅ Approvals manager found');
        
        // Test 2: Initialize approvals manager
        if (!window.approvalsManager.initialized) {
            console.log('🔄 Initializing approvals manager...');
            await window.approvalsManager.initialize();
        }
        
        console.log('✅ Approvals manager initialized');
        
        // Test 3: Check data loading
        console.log('📊 Checking loaded data:');
        console.log(`   Schools: ${window.approvalsManager.schools.length}`);
        console.log(`   Students: ${window.approvalsManager.students.length}`);
        console.log(`   Grades: ${window.approvalsManager.grades.length}`);
        console.log(`   Approvals: ${window.approvalsManager.approvals.length}`);
        
        return true;
        
    } catch (error) {
        console.error('❌ Error testing approvals system:', error);
        return false;
    }
}

// Test final result calculations
async function testFinalCalculations() {
    console.log('🧮 Testing Final Result Calculations...');
    
    if (!window.approvalsManager || !window.approvalsManager.initialized) {
        console.log('❌ Approvals manager not initialized');
        return;
    }
    
    const students = window.approvalsManager.students;
    if (students.length === 0) {
        console.log('❌ No students found for testing');
        return;
    }
    
    // Test with first student
    const testStudent = students[0];
    console.log(`🧪 Testing calculations for: ${testStudent.name}`);
    
    try {
        const result = await window.approvalsManager.calculateStudentFinalResult(testStudent);
        
        console.log('📊 Calculation Results:');
        console.log(`   Semester 1 Total: ${result.semester1Total}`);
        console.log(`   Semester 2 Total: ${result.semester2Total}`);
        console.log(`   Final Total: ${result.finalTotal}`);
        console.log(`   Average: ${result.average}`);
        console.log(`   Percentage: ${result.percentage}%`);
        console.log(`   Level: ${result.level}`);
        console.log(`   Grade: ${result.grade}`);
        console.log(`   Has Complete Data: ${result.hasCompleteData}`);
        console.log(`   Is Approved: ${result.isApproved}`);
        
        // Validate calculations
        const expectedAverage = result.finalTotal / 2;
        const averageCorrect = Math.abs(result.average - expectedAverage) < 0.01;
        console.log(`   ✅ Average calculation: ${averageCorrect ? 'Correct' : 'Incorrect'}`);
        
    } catch (error) {
        console.error('❌ Error in calculations:', error);
    }
}

// Test approvals interface
function testApprovalsInterface() {
    console.log('🖥️ Testing Approvals Interface...');
    
    // Check if we're on the approvals page
    const approvalsSection = document.getElementById('approvals');
    if (!approvalsSection || !approvalsSection.classList.contains('active')) {
        console.log('⚠️ Please navigate to the Approvals section first');
        return;
    }
    
    // Check for key UI elements
    const elements = [
        { selector: '#approvalSchool', name: 'School Filter' },
        { selector: '#approvalGrade', name: 'Grade Filter' },
        { selector: '#approvalSection', name: 'Section Filter' },
        { selector: '#loadApprovalsBtn', name: 'Load Data Button' },
        { selector: '.overview-cards', name: 'Overview Cards' },
        { selector: '.approvals-table', name: 'Approvals Table' },
        { selector: '#approveSelectedBtn', name: 'Approve Selected Button' },
        { selector: '#generateApprovalReportBtn', name: 'Generate Report Button' }
    ];
    
    elements.forEach(({ selector, name }) => {
        const element = document.querySelector(selector);
        const status = element ? '✅' : '❌';
        console.log(`${status} ${name}: ${element ? 'found' : 'not found'}`);
    });
    
    // Check overview cards content
    const totalStudents = document.getElementById('totalStudentsCount')?.textContent || '0';
    const approvedStudents = document.getElementById('approvedStudentsCount')?.textContent || '0';
    const pendingStudents = document.getElementById('pendingStudentsCount')?.textContent || '0';
    const averageGrade = document.getElementById('averageGrade')?.textContent || '0%';
    
    console.log('📊 Overview Cards Data:');
    console.log(`   Total Students: ${totalStudents}`);
    console.log(`   Approved Students: ${approvedStudents}`);
    console.log(`   Pending Students: ${pendingStudents}`);
    console.log(`   Average Grade: ${averageGrade}`);
}

// Test approval workflow
async function testApprovalWorkflow() {
    console.log('🔄 Testing Approval Workflow...');
    
    if (!window.approvalsManager || !window.approvalsManager.initialized) {
        console.log('❌ Approvals manager not initialized');
        return;
    }
    
    // Test with sample data
    const students = window.approvalsManager.students;
    if (students.length === 0) {
        console.log('❌ No students found for testing');
        return;
    }
    
    const testStudent = students[0];
    console.log(`🧪 Testing approval workflow for: ${testStudent.name}`);
    
    try {
        // Check initial approval status
        const currentYear = window.app?.settings?.currentAcademicYear || '2024-2025';
        const initialApproval = await db.getStudentApproval(testStudent.id, currentYear);
        console.log(`   Initial approval status: ${initialApproval ? 'Approved' : 'Not approved'}`);
        
        // Test approval process (without actually approving)
        console.log('   ✅ Approval process test: Ready');
        
        // Test database functions
        const allApprovals = await db.getApprovals();
        console.log(`   📊 Total approvals in database: ${allApprovals.length}`);
        
        const yearApprovals = await db.getApprovalsByYear(currentYear);
        console.log(`   📊 Approvals for ${currentYear}: ${yearApprovals.length}`);
        
    } catch (error) {
        console.error('❌ Error in approval workflow test:', error);
    }
}

// Test report generation
async function testReportGeneration() {
    console.log('📄 Testing Report Generation...');
    
    if (!window.approvalsManager || !window.approvalsManager.initialized) {
        console.log('❌ Approvals manager not initialized');
        return;
    }
    
    // Set test filters
    window.approvalsManager.currentFilters = {
        school: window.approvalsManager.schools[0]?.id || '',
        grade: '5',
        section: 'أ',
        academicYear: '2024-2025'
    };
    
    try {
        console.log('🔄 Testing report HTML generation...');
        
        // Get sample student results
        const studentResults = await window.approvalsManager.getCurrentStudentResults();
        console.log(`   📊 Student results for report: ${studentResults.length}`);
        
        if (studentResults.length > 0) {
            const school = window.approvalsManager.schools[0];
            const reportHTML = window.approvalsManager.generateApprovalReportHTML(studentResults, school);
            
            if (reportHTML && reportHTML.length > 1000) {
                console.log('   ✅ Report HTML generated successfully');
                console.log(`   📏 Report HTML length: ${reportHTML.length} characters`);
                
                // Check for key report elements
                const hasHeader = reportHTML.includes('تقرير اعتماد نتائج العام الدراسي');
                const hasTable = reportHTML.includes('<table');
                const hasSignatures = reportHTML.includes('staff-signatures');
                
                console.log(`   📋 Report header: ${hasHeader ? '✅' : '❌'}`);
                console.log(`   📊 Report table: ${hasTable ? '✅' : '❌'}`);
                console.log(`   ✍️ Staff signatures: ${hasSignatures ? '✅' : '❌'}`);
            } else {
                console.log('   ❌ Report HTML generation failed or too short');
            }
        } else {
            console.log('   ⚠️ No student results available for report');
        }
        
    } catch (error) {
        console.error('❌ Error in report generation test:', error);
    }
}

// Test data export functionality
async function testDataExport() {
    console.log('📤 Testing Data Export...');
    
    if (!window.approvalsManager || !window.approvalsManager.initialized) {
        console.log('❌ Approvals manager not initialized');
        return;
    }
    
    try {
        // Test getting current student results
        const studentResults = await window.approvalsManager.getCurrentStudentResults();
        console.log(`   📊 Student results for export: ${studentResults.length}`);
        
        if (studentResults.length > 0) {
            // Test data formatting for export
            const exportData = studentResults.map((result, index) => ({
                'الرقم': index + 1,
                'اسم الطالب': result.student.name,
                'رقم الطالب': result.student.studentId,
                'المجموع': result.finalTotal.toFixed(1),
                'النسبة': result.percentage.toFixed(1) + '%',
                'المستوى': result.level,
                'التقدير': result.grade
            }));
            
            console.log('   ✅ Export data formatted successfully');
            console.log(`   📊 Export records: ${exportData.length}`);
            console.log('   📋 Sample record:', exportData[0]);
        } else {
            console.log('   ⚠️ No data available for export');
        }
        
    } catch (error) {
        console.error('❌ Error in data export test:', error);
    }
}

// Test CSS styles
function testApprovalsStyles() {
    console.log('🎨 Testing Approvals CSS Styles...');
    
    // Test overview card styles
    const testCard = document.createElement('div');
    testCard.className = 'overview-card';
    document.body.appendChild(testCard);
    
    const cardStyles = window.getComputedStyle(testCard);
    const hasBackground = cardStyles.background !== 'rgba(0, 0, 0, 0)';
    const hasPadding = parseFloat(cardStyles.padding) > 0;
    const hasBorderRadius = parseFloat(cardStyles.borderRadius) > 0;
    
    console.log(`   📦 Overview card background: ${hasBackground ? '✅' : '❌'}`);
    console.log(`   📏 Overview card padding: ${hasPadding ? '✅' : '❌'}`);
    console.log(`   🔲 Overview card border radius: ${hasBorderRadius ? '✅' : '❌'}`);
    
    document.body.removeChild(testCard);
    
    // Test approval status styles
    const statusClasses = ['approved', 'pending', 'incomplete'];
    statusClasses.forEach(className => {
        const testStatus = document.createElement('div');
        testStatus.className = `approval-status ${className}`;
        document.body.appendChild(testStatus);
        
        const statusStyles = window.getComputedStyle(testStatus);
        const hasColor = statusStyles.color !== 'rgba(0, 0, 0, 0)';
        const hasBackgroundColor = statusStyles.backgroundColor !== 'rgba(0, 0, 0, 0)';
        
        console.log(`   🎨 ${className} status color: ${hasColor ? '✅' : '❌'}`);
        console.log(`   🎨 ${className} status background: ${hasBackgroundColor ? '✅' : '❌'}`);
        
        document.body.removeChild(testStatus);
    });
}

// Run comprehensive test
async function runApprovalsTest() {
    console.log('🚀 Running Comprehensive Approvals Test...');
    console.log('='.repeat(50));
    
    await testApprovalsSystem();
    
    setTimeout(async () => {
        await testFinalCalculations();
    }, 1000);
    
    setTimeout(() => {
        testApprovalsInterface();
    }, 2000);
    
    setTimeout(async () => {
        await testApprovalWorkflow();
    }, 3000);
    
    setTimeout(async () => {
        await testReportGeneration();
    }, 4000);
    
    setTimeout(async () => {
        await testDataExport();
    }, 5000);
    
    setTimeout(() => {
        testApprovalsStyles();
        console.log('='.repeat(50));
        console.log('✅ Approvals test completed!');
    }, 6000);
}

// Make functions available globally
window.testApprovalsSystem = testApprovalsSystem;
window.testFinalCalculations = testFinalCalculations;
window.testApprovalsInterface = testApprovalsInterface;
window.testApprovalWorkflow = testApprovalWorkflow;
window.testReportGeneration = testReportGeneration;
window.testDataExport = testDataExport;
window.testApprovalsStyles = testApprovalsStyles;
window.runApprovalsTest = runApprovalsTest;

// Auto-run basic test
console.log(`
🏆 Approvals Test Commands Available:
- testApprovalsSystem() - Test basic system functionality
- testFinalCalculations() - Test grade calculations
- testApprovalsInterface() - Test UI components
- testApprovalWorkflow() - Test approval process
- testReportGeneration() - Test report creation
- testDataExport() - Test data export
- testApprovalsStyles() - Test CSS styles
- runApprovalsTest() - Run all tests

Quick start: runApprovalsTest()
`);

// Auto-run if approvals section is active
if (document.getElementById('approvals')?.classList.contains('active')) {
    console.log('🔄 Approvals section is active, running basic test...');
    testApprovalsSystem();
}
