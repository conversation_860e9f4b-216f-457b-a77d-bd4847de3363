// Statistics and analytics management system

class StatisticsManager {
    constructor() {
        this.students = [];
        this.schools = [];
        this.grades = [];
        this.charts = {};
        this.currentFilters = {
            school: '',
            grade: '',
            semester: '',
            academicYear: '2024-2025'
        };
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Filter change listeners will be added after initialization
    }

    async initialize() {
        try {
            [this.students, this.schools, this.grades] = await Promise.all([
                db.getStudents(),
                db.getSchools(),
                db.getGrades()
            ]);
            
            this.renderStatisticsInterface();
            this.loadInitialStatistics();
        } catch (error) {
            console.error('Error initializing statistics manager:', error);
            Utils.showNotification('خطأ في تحميل البيانات', 'error');
        }
    }

    renderStatisticsInterface() {
        const container = document.querySelector('#statistics .statistics-container');
        if (!container) return;

        container.innerHTML = `
            <div class="statistics-dashboard">
                <!-- Filters Section -->
                <div class="statistics-filters">
                    <h3>فلاتر الإحصائيات</h3>
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label>المدرسة</label>
                            <select id="statsSchoolFilter" class="form-select">
                                <option value="">جميع المدارس</option>
                                ${this.schools.map(school => 
                                    `<option value="${school.id}">${school.name}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>الصف الدراسي</label>
                            <select id="statsGradeFilter" class="form-select">
                                <option value="">جميع الصفوف</option>
                                ${this.generateGradeOptions()}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>الفصل الدراسي</label>
                            <select id="statsSemesterFilter" class="form-select">
                                <option value="">كلا الفصلين</option>
                                <option value="1">الفصل الأول</option>
                                <option value="2">الفصل الثاني</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>العام الدراسي</label>
                            <select id="statsAcademicYearFilter" class="form-select">
                                <option value="2024-2025">2024-2025</option>
                                <option value="2023-2024">2023-2024</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <button class="btn btn-primary" onclick="statisticsManager.updateStatistics()">
                                <i class="fas fa-sync"></i> تحديث الإحصائيات
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Overview Cards -->
                <div class="stats-overview">
                    <div class="stats-cards">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3>إجمالي الطلاب</h3>
                                <span class="stat-number" id="totalStudents">0</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h3>المتوسط العام</h3>
                                <span class="stat-number" id="overallAverage">0</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3>نسبة النجاح</h3>
                                <span class="stat-number" id="passRate">0%</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="stat-content">
                                <h3>المتفوقون (أ)</h3>
                                <span class="stat-number" id="excellentCount">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="charts-section">
                    <div class="charts-grid">
                        <!-- Grade Distribution Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>توزيع المستويات</h3>
                            </div>
                            <canvas id="gradeDistributionChart"></canvas>
                        </div>

                        <!-- Performance Trend Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>اتجاه الأداء</h3>
                            </div>
                            <canvas id="performanceTrendChart"></canvas>
                        </div>

                        <!-- School Comparison Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>مقارنة المدارس</h3>
                            </div>
                            <canvas id="schoolComparisonChart"></canvas>
                        </div>

                        <!-- Subject Performance Chart -->
                        <div class="chart-container">
                            <div class="chart-header">
                                <h3>أداء المكونات</h3>
                            </div>
                            <canvas id="subjectPerformanceChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Detailed Statistics -->
                <div class="detailed-stats">
                    <div class="stats-tabs">
                        <button class="tab-btn active" data-tab="distribution">توزيع الدرجات</button>
                        <button class="tab-btn" data-tab="schools">إحصائيات المدارس</button>
                        <button class="tab-btn" data-tab="grades">إحصائيات الصفوف</button>
                        <button class="tab-btn" data-tab="analysis">تحليل الأداء</button>
                    </div>

                    <div class="tab-content">
                        <div id="distributionTab" class="tab-pane active">
                            <div id="distributionTable"></div>
                        </div>
                        <div id="schoolsTab" class="tab-pane">
                            <div id="schoolsTable"></div>
                        </div>
                        <div id="gradesTab" class="tab-pane">
                            <div id="gradesTable"></div>
                        </div>
                        <div id="analysisTab" class="tab-pane">
                            <div id="analysisContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.addFilterListeners();
        this.addTabListeners();
    }

    addFilterListeners() {
        ['statsSchoolFilter', 'statsGradeFilter', 'statsSemesterFilter', 'statsAcademicYearFilter'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    const filterKey = id.replace('stats', '').replace('Filter', '').toLowerCase();
                    this.currentFilters[filterKey] = e.target.value;
                    this.updateStatistics();
                });
            }
        });
    }

    addTabListeners() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                this.switchTab(tabName);
            });
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(`${tabName}Tab`).classList.add('active');

        // Load tab content
        this.loadTabContent(tabName);
    }

    generateGradeOptions() {
        let options = '';
        for (let i = 1; i <= 12; i++) {
            options += `<option value="${i}">الصف ${this.getGradeName(i)}</option>`;
        }
        return options;
    }

    async loadInitialStatistics() {
        await this.updateStatistics();
    }

    async updateStatistics() {
        try {
            const filteredData = this.getFilteredData();
            
            // Update overview cards
            this.updateOverviewCards(filteredData);
            
            // Update charts
            this.updateCharts(filteredData);
            
            // Update current tab content
            const activeTab = document.querySelector('.tab-btn.active');
            if (activeTab) {
                this.loadTabContent(activeTab.dataset.tab);
            }

        } catch (error) {
            console.error('Error updating statistics:', error);
            Utils.showNotification('خطأ في تحديث الإحصائيات', 'error');
        }
    }

    getFilteredData() {
        let filteredGrades = this.grades;

        // Apply filters
        if (this.currentFilters.school) {
            filteredGrades = filteredGrades.filter(g => g.schoolId === this.currentFilters.school);
        }
        if (this.currentFilters.grade) {
            filteredGrades = filteredGrades.filter(g => g.grade === parseInt(this.currentFilters.grade));
        }
        if (this.currentFilters.semester) {
            filteredGrades = filteredGrades.filter(g => g.semester === parseInt(this.currentFilters.semester));
        }
        if (this.currentFilters.academicyear) {
            filteredGrades = filteredGrades.filter(g => g.academicYear === this.currentFilters.academicyear);
        }

        // Combine with student and school data
        return filteredGrades.map(grade => {
            const student = this.students.find(s => s.id === grade.studentId);
            const school = this.schools.find(s => s.id === grade.schoolId);
            const calculated = Utils.calculateGrades(grade.grade, grade.grades);

            return {
                grade,
                student,
                school,
                calculated
            };
        }).filter(item => item.student && item.school);
    }

    updateOverviewCards(data) {
        const statistics = Utils.calculateStatistics(data.map(item => item.calculated));
        
        document.getElementById('totalStudents').textContent = statistics.total;
        document.getElementById('overallAverage').textContent = statistics.average;
        document.getElementById('passRate').textContent = statistics.passRate + '%';
        document.getElementById('excellentCount').textContent = statistics.distribution['أ'] || 0;
    }

    updateCharts(data) {
        this.updateGradeDistributionChart(data);
        this.updatePerformanceTrendChart(data);
        this.updateSchoolComparisonChart(data);
        this.updateSubjectPerformanceChart(data);
    }

    updateGradeDistributionChart(data) {
        const ctx = document.getElementById('gradeDistributionChart');
        if (!ctx) return;

        const statistics = Utils.calculateStatistics(data.map(item => item.calculated));
        
        // Destroy existing chart
        if (this.charts.gradeDistribution) {
            this.charts.gradeDistribution.destroy();
        }

        this.charts.gradeDistribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(statistics.distribution),
                datasets: [{
                    data: Object.values(statistics.distribution),
                    backgroundColor: [
                        '#28a745', // أ - Green
                        '#17a2b8', // ب - Cyan
                        '#ffc107', // ج - Yellow
                        '#fd7e14', // د - Orange
                        '#dc3545', // هـ - Red
                        '#6c757d'  // راسب - Gray
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    updatePerformanceTrendChart(data) {
        const ctx = document.getElementById('performanceTrendChart');
        if (!ctx) return;

        // Group data by grade level
        const gradeGroups = {};
        data.forEach(item => {
            const grade = item.student.grade;
            if (!gradeGroups[grade]) {
                gradeGroups[grade] = [];
            }
            gradeGroups[grade].push(item.calculated.total);
        });

        const labels = Object.keys(gradeGroups).sort((a, b) => a - b).map(g => this.getGradeName(parseInt(g)));
        const averages = Object.keys(gradeGroups).sort((a, b) => a - b).map(grade => {
            const scores = gradeGroups[grade];
            return scores.reduce((sum, score) => sum + score, 0) / scores.length;
        });

        // Destroy existing chart
        if (this.charts.performanceTrend) {
            this.charts.performanceTrend.destroy();
        }

        this.charts.performanceTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'متوسط الدرجات',
                    data: averages,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                }
            }
        });
    }

    updateSchoolComparisonChart(data) {
        const ctx = document.getElementById('schoolComparisonChart');
        if (!ctx) return;

        // Group data by school
        const schoolGroups = {};
        data.forEach(item => {
            const schoolId = item.school.id;
            if (!schoolGroups[schoolId]) {
                schoolGroups[schoolId] = {
                    name: item.school.name,
                    scores: []
                };
            }
            schoolGroups[schoolId].scores.push(item.calculated.total);
        });

        const schoolNames = Object.values(schoolGroups).map(school => school.name);
        const schoolAverages = Object.values(schoolGroups).map(school => {
            return school.scores.reduce((sum, score) => sum + score, 0) / school.scores.length;
        });

        // Destroy existing chart
        if (this.charts.schoolComparison) {
            this.charts.schoolComparison.destroy();
        }

        this.charts.schoolComparison = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: schoolNames,
                datasets: [{
                    label: 'متوسط المدرسة',
                    data: schoolAverages,
                    backgroundColor: [
                        '#667eea', '#764ba2', '#f093fb', '#f5576c',
                        '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    },
                    x: {
                        ticks: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                }
            }
        });
    }

    updateSubjectPerformanceChart(data) {
        const ctx = document.getElementById('subjectPerformanceChart');
        if (!ctx) return;

        if (data.length === 0) return;

        // Calculate average performance for each component
        const components = {};
        data.forEach(item => {
            const gradeStructure = Utils.getGradeStructure(item.student.grade);
            const grades = item.grade.grades;

            Object.keys(gradeStructure).forEach(key => {
                if (key === 'total') return;

                if (!components[key]) {
                    components[key] = {
                        label: gradeStructure[key].label,
                        total: 0,
                        count: 0,
                        max: gradeStructure[key].max
                    };
                }

                if (grades[key] !== undefined && grades[key] !== null) {
                    components[key].total += grades[key];
                    components[key].count++;
                }
            });
        });

        const labels = Object.values(components).map(comp => comp.label);
        const averages = Object.values(components).map(comp =>
            comp.count > 0 ? comp.total / comp.count : 0
        );
        const maxScores = Object.values(components).map(comp => comp.max);

        // Destroy existing chart
        if (this.charts.subjectPerformance) {
            this.charts.subjectPerformance.destroy();
        }

        this.charts.subjectPerformance = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'متوسط الأداء',
                    data: averages,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.2)',
                    borderWidth: 2
                }, {
                    label: 'الدرجة الكاملة',
                    data: maxScores,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 1,
                    borderDash: [5, 5]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        ticks: {
                            font: {
                                family: 'Cairo'
                            }
                        },
                        pointLabels: {
                            font: {
                                family: 'Cairo'
                            }
                        }
                    }
                }
            }
        });
    }

    loadTabContent(tabName) {
        const data = this.getFilteredData();

        switch (tabName) {
            case 'distribution':
                this.loadDistributionTab(data);
                break;
            case 'schools':
                this.loadSchoolsTab(data);
                break;
            case 'grades':
                this.loadGradesTab(data);
                break;
            case 'analysis':
                this.loadAnalysisTab(data);
                break;
        }
    }

    loadDistributionTab(data) {
        const container = document.getElementById('distributionTable');
        if (!container) return;

        const statistics = Utils.calculateStatistics(data.map(item => item.calculated));

        container.innerHTML = `
            <div class="distribution-details">
                <h4>تفاصيل توزيع المستويات</h4>
                <table class="table">
                    <thead>
                        <tr>
                            <th>المستوى</th>
                            <th>عدد الطلاب</th>
                            <th>النسبة المئوية</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${Object.entries(statistics.distribution).map(([level, count]) => {
                            const percentage = statistics.total > 0 ? ((count / statistics.total) * 100).toFixed(1) : 0;
                            const description = this.getLevelDescription(level);
                            return `
                                <tr>
                                    <td class="level-cell">${level}</td>
                                    <td>${count}</td>
                                    <td>${percentage}%</td>
                                    <td>${description}</td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    loadSchoolsTab(data) {
        const container = document.getElementById('schoolsTable');
        if (!container) return;

        // Group data by school
        const schoolStats = {};
        data.forEach(item => {
            const schoolId = item.school.id;
            if (!schoolStats[schoolId]) {
                schoolStats[schoolId] = {
                    name: item.school.name,
                    students: [],
                    totalScore: 0,
                    passedCount: 0
                };
            }

            schoolStats[schoolId].students.push(item.calculated);
            schoolStats[schoolId].totalScore += item.calculated.total;

            if (item.calculated.level !== 'راسب' && item.calculated.level !== 'ز') {
                schoolStats[schoolId].passedCount++;
            }
        });

        const schoolsData = Object.values(schoolStats).map(school => ({
            ...school,
            studentCount: school.students.length,
            average: school.students.length > 0 ? (school.totalScore / school.students.length).toFixed(2) : 0,
            passRate: school.students.length > 0 ? ((school.passedCount / school.students.length) * 100).toFixed(1) : 0,
            distribution: Utils.calculateStatistics(school.students).distribution
        })).sort((a, b) => b.average - a.average);

        container.innerHTML = `
            <div class="schools-statistics">
                <h4>إحصائيات المدارس</h4>
                <table class="table">
                    <thead>
                        <tr>
                            <th>المدرسة</th>
                            <th>عدد الطلاب</th>
                            <th>المتوسط</th>
                            <th>نسبة النجاح</th>
                            <th>المتفوقون (أ)</th>
                            <th>الراسبون</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${schoolsData.map(school => `
                            <tr>
                                <td>${school.name}</td>
                                <td>${school.studentCount}</td>
                                <td>${school.average}</td>
                                <td>${school.passRate}%</td>
                                <td>${school.distribution['أ'] || 0}</td>
                                <td>${school.distribution['راسب'] || 0}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    loadGradesTab(data) {
        const container = document.getElementById('gradesTable');
        if (!container) return;

        // Group data by grade level
        const gradeStats = {};
        data.forEach(item => {
            const grade = item.student.grade;
            if (!gradeStats[grade]) {
                gradeStats[grade] = {
                    grade: grade,
                    gradeName: this.getGradeName(grade),
                    students: [],
                    totalScore: 0,
                    passedCount: 0
                };
            }

            gradeStats[grade].students.push(item.calculated);
            gradeStats[grade].totalScore += item.calculated.total;

            if (item.calculated.level !== 'راسب' && item.calculated.level !== 'ز') {
                gradeStats[grade].passedCount++;
            }
        });

        const gradesData = Object.values(gradeStats).map(grade => ({
            ...grade,
            studentCount: grade.students.length,
            average: grade.students.length > 0 ? (grade.totalScore / grade.students.length).toFixed(2) : 0,
            passRate: grade.students.length > 0 ? ((grade.passedCount / grade.students.length) * 100).toFixed(1) : 0,
            distribution: Utils.calculateStatistics(grade.students).distribution
        })).sort((a, b) => a.grade - b.grade);

        container.innerHTML = `
            <div class="grades-statistics">
                <h4>إحصائيات الصفوف الدراسية</h4>
                <table class="table">
                    <thead>
                        <tr>
                            <th>الصف الدراسي</th>
                            <th>عدد الطلاب</th>
                            <th>المتوسط</th>
                            <th>نسبة النجاح</th>
                            <th>المتفوقون (أ)</th>
                            <th>الراسبون</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${gradesData.map(grade => `
                            <tr>
                                <td>${grade.gradeName}</td>
                                <td>${grade.studentCount}</td>
                                <td>${grade.average}</td>
                                <td>${grade.passRate}%</td>
                                <td>${grade.distribution['أ'] || 0}</td>
                                <td>${grade.distribution['راسب'] || 0}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    loadAnalysisTab(data) {
        const container = document.getElementById('analysisContent');
        if (!container) return;

        const statistics = Utils.calculateStatistics(data.map(item => item.calculated));

        // Calculate additional analysis metrics
        const strengthsWeaknesses = this.analyzeStrengthsWeaknesses(data);
        const recommendations = this.generateRecommendations(statistics, data);

        container.innerHTML = `
            <div class="performance-analysis">
                <h4>تحليل الأداء المفصل</h4>

                <div class="analysis-sections">
                    <div class="analysis-section">
                        <h5>نقاط القوة</h5>
                        <ul class="strengths-list">
                            ${strengthsWeaknesses.strengths.map(strength => `<li>${strength}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="analysis-section">
                        <h5>نقاط الضعف</h5>
                        <ul class="weaknesses-list">
                            ${strengthsWeaknesses.weaknesses.map(weakness => `<li>${weakness}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="analysis-section">
                        <h5>التوصيات</h5>
                        <ul class="recommendations-list">
                            ${recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="analysis-section">
                        <h5>المؤشرات الرئيسية</h5>
                        <div class="key-metrics">
                            <div class="metric">
                                <span class="metric-label">معدل التحسن المطلوب:</span>
                                <span class="metric-value">${this.calculateImprovementRate(statistics)}%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">مستوى الأداء العام:</span>
                                <span class="metric-value">${this.getOverallPerformanceLevel(statistics.average)}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">التوزيع المثالي:</span>
                                <span class="metric-value">${this.isDistributionIdeal(statistics.distribution) ? 'متحقق' : 'غير متحقق'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Helper methods
    getLevelDescription(level) {
        const descriptions = {
            'أ': 'ممتاز',
            'ب': 'جيد جداً',
            'ج': 'جيد',
            'د': 'مقبول',
            'هـ': 'ضعيف',
            'راسب': 'راسب',
            'ز': 'ضعيف جداً'
        };
        return descriptions[level] || level;
    }

    getGradeName(grade) {
        const names = {
            1: 'الأول', 2: 'الثاني', 3: 'الثالث', 4: 'الرابع',
            5: 'الخامس', 6: 'السادس', 7: 'السابع', 8: 'الثامن',
            9: 'التاسع', 10: 'العاشر', 11: 'الحادي عشر', 12: 'الثاني عشر'
        };
        return names[grade] || grade.toString();
    }

    analyzeStrengthsWeaknesses(data) {
        const statistics = Utils.calculateStatistics(data.map(item => item.calculated));
        const strengths = [];
        const weaknesses = [];

        // Analyze pass rate
        if (statistics.passRate >= 90) {
            strengths.push(`نسبة نجاح عالية جداً (${statistics.passRate}%)`);
        } else if (statistics.passRate >= 75) {
            strengths.push(`نسبة نجاح جيدة (${statistics.passRate}%)`);
        } else if (statistics.passRate < 60) {
            weaknesses.push(`نسبة نجاح منخفضة (${statistics.passRate}%)`);
        }

        // Analyze average
        if (statistics.average >= 85) {
            strengths.push(`متوسط عام ممتاز (${statistics.average})`);
        } else if (statistics.average >= 70) {
            strengths.push(`متوسط عام جيد (${statistics.average})`);
        } else if (statistics.average < 60) {
            weaknesses.push(`متوسط عام منخفض (${statistics.average})`);
        }

        // Analyze distribution
        const excellentPercentage = statistics.total > 0 ? (statistics.distribution['أ'] / statistics.total) * 100 : 0;
        const failedPercentage = statistics.total > 0 ? ((statistics.distribution['راسب'] || 0) / statistics.total) * 100 : 0;

        if (excellentPercentage >= 30) {
            strengths.push(`نسبة عالية من المتفوقين (${excellentPercentage.toFixed(1)}%)`);
        } else if (excellentPercentage < 10) {
            weaknesses.push(`نسبة منخفضة من المتفوقين (${excellentPercentage.toFixed(1)}%)`);
        }

        if (failedPercentage > 20) {
            weaknesses.push(`نسبة عالية من الراسبين (${failedPercentage.toFixed(1)}%)`);
        } else if (failedPercentage <= 5) {
            strengths.push(`نسبة منخفضة من الراسبين (${failedPercentage.toFixed(1)}%)`);
        }

        // Default messages if no specific strengths/weaknesses found
        if (strengths.length === 0) {
            strengths.push('الأداء ضمن المعدل المتوقع');
        }
        if (weaknesses.length === 0) {
            weaknesses.push('لا توجد نقاط ضعف واضحة');
        }

        return { strengths, weaknesses };
    }

    generateRecommendations(statistics, data) {
        const recommendations = [];

        // Based on pass rate
        if (statistics.passRate < 70) {
            recommendations.push('تطبيق برامج تقوية شاملة للطلاب المتعثرين');
            recommendations.push('مراجعة طرق التدريس والتقييم المستخدمة');
        }

        // Based on average
        if (statistics.average < 70) {
            recommendations.push('تحسين جودة المحتوى التعليمي والأنشطة');
            recommendations.push('توفير موارد تعليمية إضافية');
        }

        // Based on distribution
        const excellentPercentage = statistics.total > 0 ? (statistics.distribution['أ'] / statistics.total) * 100 : 0;
        if (excellentPercentage < 15) {
            recommendations.push('تطوير برامج إثراء للطلاب المتميزين');
            recommendations.push('رفع مستوى التحدي في المناهج والأنشطة');
        }

        const failedPercentage = statistics.total > 0 ? ((statistics.distribution['راسب'] || 0) / statistics.total) * 100 : 0;
        if (failedPercentage > 15) {
            recommendations.push('تطبيق نظام الإنذار المبكر للطلاب المعرضين للرسوب');
            recommendations.push('تفعيل دور الإرشاد الأكاديمي والنفسي');
        }

        // General recommendations
        recommendations.push('متابعة دورية لتقييم فعالية البرامج التعليمية');
        recommendations.push('تطوير شراكة فعالة مع أولياء الأمور');

        return recommendations;
    }

    calculateImprovementRate(statistics) {
        const targetPassRate = 85; // Target pass rate
        const currentPassRate = statistics.passRate;

        if (currentPassRate >= targetPassRate) {
            return 0; // No improvement needed
        }

        return Math.round(targetPassRate - currentPassRate);
    }

    getOverallPerformanceLevel(average) {
        if (average >= 90) return 'ممتاز';
        if (average >= 80) return 'جيد جداً';
        if (average >= 70) return 'جيد';
        if (average >= 60) return 'مقبول';
        return 'يحتاج تحسين';
    }

    isDistributionIdeal(distribution) {
        const total = Object.values(distribution).reduce((sum, count) => sum + count, 0);
        if (total === 0) return false;

        const excellentPercentage = (distribution['أ'] || 0) / total * 100;
        const failedPercentage = (distribution['راسب'] || 0) / total * 100;

        // Ideal distribution: 20-40% excellent, less than 10% failed
        return excellentPercentage >= 20 && excellentPercentage <= 40 && failedPercentage < 10;
    }

    // Export statistics
    async exportStatistics() {
        try {
            const data = this.getFilteredData();
            const statistics = Utils.calculateStatistics(data.map(item => item.calculated));

            const exportData = [{
                'المؤشر': 'إجمالي الطلاب',
                'القيمة': statistics.total
            }, {
                'المؤشر': 'المتوسط العام',
                'القيمة': statistics.average
            }, {
                'المؤشر': 'نسبة النجاح',
                'القيمة': statistics.passRate + '%'
            }, {
                'المؤشر': 'الطلاب الناجحون',
                'القيمة': statistics.passed
            }, {
                'المؤشر': 'الطلاب الراسبون',
                'القيمة': statistics.failed
            }];

            // Add distribution data
            Object.entries(statistics.distribution).forEach(([level, count]) => {
                exportData.push({
                    'المؤشر': `مستوى ${level}`,
                    'القيمة': count
                });
            });

            Utils.exportToExcel(exportData, `إحصائيات_${new Date().toISOString().split('T')[0]}.xlsx`);
            Utils.showNotification('تم تصدير الإحصائيات بنجاح', 'success');

        } catch (error) {
            console.error('Error exporting statistics:', error);
            Utils.showNotification('خطأ في تصدير الإحصائيات', 'error');
        }
    }
}

// Create global statistics manager instance
const statisticsManager = new StatisticsManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize statistics manager when statistics section is first shown
    const statisticsSection = document.getElementById('statistics');
    if (statisticsSection) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (statisticsSection.classList.contains('active') && !statisticsManager.initialized) {
                        statisticsManager.initialize();
                        statisticsManager.initialized = true;
                    }
                }
            });
        });

        observer.observe(statisticsSection, { attributes: true });
    }
});

// Make statisticsManager available globally
window.statisticsManager = statisticsManager;
