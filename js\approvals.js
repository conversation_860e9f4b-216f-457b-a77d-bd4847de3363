// Academic Year Approvals Management System

class ApprovalsManager {
    constructor() {
        this.schools = [];
        this.students = [];
        this.grades = [];
        this.approvals = [];
        this.initialized = false;
        this.currentFilters = {
            school: '',
            grade: '',
            section: '',
            academicYear: ''
        };
    }

    async initialize() {
        try {
            await this.loadData();
            this.renderApprovalsInterface();
            this.setupEventListeners();
            this.initialized = true;
            console.log('Approvals manager initialized successfully');
        } catch (error) {
            console.error('Error initializing approvals manager:', error);
            Utils.showNotification('خطأ في تحميل نظام الاعتمادات', 'error');
        }
    }

    async loadData() {
        try {
            this.schools = await db.getSchools() || [];
            this.students = await db.getStudents() || [];
            this.grades = await db.getGrades() || [];
            this.approvals = await db.getApprovals() || [];
        } catch (error) {
            console.error('Error loading approvals data:', error);
            throw error;
        }
    }

    renderApprovalsInterface() {
        const container = document.querySelector('#approvals .approvals-container');
        if (!container) return;

        const currentYear = window.app?.settings?.currentAcademicYear || '2024-2025';

        container.innerHTML = `
            <div class="approvals-dashboard">
                <!-- Filters Section -->
                <div class="approvals-filters">
                    <div class="filter-group">
                        <label for="approvalSchool">المدرسة</label>
                        <select id="approvalSchool" class="form-select">
                            <option value="">جميع المدارس</option>
                            ${this.schools.map(school => 
                                `<option value="${school.id}">${school.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="approvalGrade">الصف الدراسي</label>
                        <select id="approvalGrade" class="form-select">
                            <option value="">جميع الصفوف</option>
                            ${Array.from({length: 12}, (_, i) => i + 1).map(grade => 
                                `<option value="${grade}">الصف ${this.getGradeName(grade)}</option>`
                            ).join('')}
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="approvalSection">الشعبة</label>
                        <input type="text" id="approvalSection" class="form-input" placeholder="مثال: أ، ب، ج">
                    </div>
                    
                    <div class="filter-group">
                        <label for="approvalYear">العام الدراسي</label>
                        <select id="approvalYear" class="form-select">
                            <option value="${currentYear}">${currentYear}</option>
                        </select>
                    </div>
                    
                    <div class="filter-actions">
                        <button class="btn btn-primary" id="loadApprovalsBtn">
                            <i class="fas fa-search"></i> عرض البيانات
                        </button>
                        <button class="btn btn-secondary" id="clearFiltersBtn">
                            <i class="fas fa-times"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>

                <!-- Approval Status Overview -->
                <div class="approval-overview">
                    <div class="overview-cards">
                        <div class="overview-card">
                            <div class="card-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="totalStudentsCount">0</h3>
                                <p>إجمالي الطلاب</p>
                            </div>
                        </div>
                        
                        <div class="overview-card approved">
                            <div class="card-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="approvedStudentsCount">0</h3>
                                <p>الطلاب المعتمدين</p>
                            </div>
                        </div>
                        
                        <div class="overview-card pending">
                            <div class="card-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="pendingStudentsCount">0</h3>
                                <p>في انتظار الاعتماد</p>
                            </div>
                        </div>
                        
                        <div class="overview-card average">
                            <div class="card-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="card-content">
                                <h3 id="averageGrade">0%</h3>
                                <p>متوسط النسبة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Approvals Table -->
                <div class="approvals-table-container">
                    <div class="table-header">
                        <h3>نتائج الطلاب للاعتماد</h3>
                        <div class="table-actions">
                            <button class="btn btn-success" id="approveSelectedBtn" disabled>
                                <i class="fas fa-check"></i> اعتماد المحدد
                            </button>
                            <button class="btn btn-warning" id="unapproveSelectedBtn" disabled>
                                <i class="fas fa-times"></i> إلغاء الاعتماد
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="approvals-table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAllApprovals">
                                    </th>
                                    <th>اسم الطالب</th>
                                    <th>رقم الطالب</th>
                                    <th>المدرسة</th>
                                    <th>الصف</th>
                                    <th>الشعبة</th>
                                    <th>الفصل الأول</th>
                                    <th>الفصل الثاني</th>
                                    <th>المجموع</th>
                                    <th>المتوسط</th>
                                    <th>النسبة</th>
                                    <th>المستوى</th>
                                    <th>التقدير</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="approvalsTableBody">
                                <!-- Approval rows will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Approval Actions -->
                <div class="approval-actions">
                    <div class="action-group">
                        <h4>إجراءات الاعتماد</h4>
                        <div class="action-buttons">
                            <button class="btn btn-success" id="approveGradeBtn">
                                <i class="fas fa-certificate"></i> اعتماد الصف الحالي
                            </button>
                            <button class="btn btn-info" id="generateApprovalReportBtn">
                                <i class="fas fa-file-alt"></i> تقرير الاعتماد
                            </button>
                            <button class="btn btn-warning" id="exportApprovalDataBtn">
                                <i class="fas fa-download"></i> تصدير البيانات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.updateOverviewCards();
    }

    setupEventListeners() {
        // Filter controls
        document.getElementById('loadApprovalsBtn')?.addEventListener('click', () => {
            this.loadApprovalsData();
        });

        document.getElementById('clearFiltersBtn')?.addEventListener('click', () => {
            this.clearFilters();
        });

        // Approval actions
        document.getElementById('approveSelectedBtn')?.addEventListener('click', () => {
            this.approveSelected();
        });

        document.getElementById('unapproveSelectedBtn')?.addEventListener('click', () => {
            this.unapproveSelected();
        });

        document.getElementById('approveGradeBtn')?.addEventListener('click', () => {
            this.approveCurrentGrade();
        });

        document.getElementById('approveAllBtn')?.addEventListener('click', () => {
            this.approveAllGrades();
        });

        // Export and report actions
        document.getElementById('generateApprovalReportBtn')?.addEventListener('click', () => {
            this.generateApprovalReport();
        });

        document.getElementById('exportApprovalDataBtn')?.addEventListener('click', () => {
            this.exportApprovalData();
        });

        document.getElementById('exportApprovedBtn')?.addEventListener('click', () => {
            this.exportApprovedResults();
        });

        // Select all checkbox
        document.getElementById('selectAllApprovals')?.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // Filter change listeners
        ['approvalSchool', 'approvalGrade', 'approvalSection'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', () => {
                this.updateFilters();
            });
        });
    }

    updateFilters() {
        this.currentFilters = {
            school: document.getElementById('approvalSchool')?.value || '',
            grade: document.getElementById('approvalGrade')?.value || '',
            section: document.getElementById('approvalSection')?.value.trim() || '',
            academicYear: document.getElementById('approvalYear')?.value || ''
        };
    }

    clearFilters() {
        document.getElementById('approvalSchool').value = '';
        document.getElementById('approvalGrade').value = '';
        document.getElementById('approvalSection').value = '';
        this.currentFilters = {
            school: '',
            grade: '',
            section: '',
            academicYear: document.getElementById('approvalYear')?.value || ''
        };
        this.loadApprovalsData();
    }

    async loadApprovalsData() {
        try {
            this.updateFilters();
            
            // Filter students based on current filters
            let filteredStudents = this.students.filter(student => {
                if (this.currentFilters.school && student.schoolId !== this.currentFilters.school) return false;
                if (this.currentFilters.grade && student.grade.toString() !== this.currentFilters.grade) return false;
                if (this.currentFilters.section && student.section !== this.currentFilters.section) return false;
                return true;
            });

            // Calculate final results for each student
            const studentResults = await Promise.all(filteredStudents.map(async (student) => {
                return await this.calculateStudentFinalResult(student);
            }));

            this.renderApprovalsTable(studentResults);
            this.updateOverviewCards(studentResults);

        } catch (error) {
            console.error('Error loading approvals data:', error);
            Utils.showNotification('خطأ في تحميل بيانات الاعتماد', 'error');
        }
    }

    async calculateStudentFinalResult(student) {
        try {
            const currentYear = this.currentFilters.academicYear || window.app?.settings?.currentAcademicYear || '2024-2025';
            
            // Get grades for both semesters
            const semester1Grades = this.grades.filter(grade => 
                grade.studentId === student.id && 
                grade.academicYear === currentYear && 
                grade.semester === 1
            );

            const semester2Grades = this.grades.filter(grade => 
                grade.studentId === student.id && 
                grade.academicYear === currentYear && 
                grade.semester === 2
            );

            // Calculate semester totals
            const semester1Total = this.calculateSemesterTotal(semester1Grades, student.grade);
            const semester2Total = this.calculateSemesterTotal(semester2Grades, student.grade);

            // Calculate final results
            const finalTotal = semester1Total + semester2Total;
            const maxTotal = this.getMaxTotalForGrade(student.grade) * 2; // Two semesters
            const average = finalTotal / 2;
            const percentage = (finalTotal / maxTotal) * 100;
            const level = Utils.getLevel5to12(percentage); // Use percentage for level
            const grade = Utils.getDescription5to12(percentage);

            // Check if already approved
            const existingApproval = this.approvals.find(approval => 
                approval.studentId === student.id && 
                approval.academicYear === currentYear
            );

            return {
                student,
                semester1Total,
                semester2Total,
                finalTotal,
                average,
                percentage: Math.round(percentage * 100) / 100,
                level,
                grade,
                isApproved: !!existingApproval,
                approvalDate: existingApproval?.approvalDate || null,
                hasCompleteData: semester1Total > 0 && semester2Total > 0
            };

        } catch (error) {
            console.error('Error calculating student result:', error);
            return {
                student,
                semester1Total: 0,
                semester2Total: 0,
                finalTotal: 0,
                average: 0,
                percentage: 0,
                level: '-',
                grade: '-',
                isApproved: false,
                approvalDate: null,
                hasCompleteData: false
            };
        }
    }

    calculateSemesterTotal(semesterGrades, gradeLevel) {
        if (!semesterGrades || semesterGrades.length === 0) return 0;

        let total = 0;
        semesterGrades.forEach(gradeRecord => {
            const calculated = Utils.calculateGrades(gradeLevel, gradeRecord.grades);
            total += calculated.total;
        });

        return total;
    }

    getMaxTotalForGrade(gradeLevel) {
        if (gradeLevel >= 1 && gradeLevel <= 4) return 50;
        if (gradeLevel >= 5 && gradeLevel <= 12) return 100;
        return 100;
    }

    renderApprovalsTable(studentResults) {
        const tbody = document.getElementById('approvalsTableBody');
        if (!tbody) return;

        if (!studentResults || studentResults.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="15" class="no-data">
                        <i class="fas fa-info-circle"></i>
                        لا توجد بيانات للعرض. يرجى اختيار المرشحات وتحميل البيانات.
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = studentResults.map(result => this.renderApprovalRow(result)).join('');
        this.setupTableEventListeners();
    }

    renderApprovalRow(result) {
        const { student, semester1Total, semester2Total, finalTotal, average, percentage, level, grade, isApproved, hasCompleteData } = result;
        const school = this.schools.find(s => s.id === student.schoolId);
        const schoolName = school ? school.name : 'غير محدد';

        const statusClass = isApproved ? 'approved' : (hasCompleteData ? 'pending' : 'incomplete');
        const statusText = isApproved ? 'معتمد' : (hasCompleteData ? 'في انتظار الاعتماد' : 'بيانات ناقصة');
        const statusIcon = isApproved ? 'fa-check-circle' : (hasCompleteData ? 'fa-clock' : 'fa-exclamation-triangle');

        return `
            <tr data-student-id="${student.id}" class="approval-row ${statusClass}">
                <td>
                    <input type="checkbox" class="student-checkbox" value="${student.id}" ${!hasCompleteData ? 'disabled' : ''}>
                </td>
                <td class="student-name">${student.name}</td>
                <td class="student-id">${student.studentId}</td>
                <td>${schoolName}</td>
                <td>${this.getGradeName(student.grade)}</td>
                <td>${student.section}</td>
                <td class="semester-total">${semester1Total.toFixed(1)}</td>
                <td class="semester-total">${semester2Total.toFixed(1)}</td>
                <td class="final-total">${finalTotal.toFixed(1)}</td>
                <td class="average">${average.toFixed(1)}</td>
                <td class="percentage">${percentage.toFixed(1)}%</td>
                <td class="grade-level" data-level="${level}">${level}</td>
                <td class="grade-description">${grade}</td>
                <td class="approval-status ${statusClass}">
                    <i class="fas ${statusIcon}"></i>
                    ${statusText}
                </td>
                <td class="action-buttons">
                    ${hasCompleteData ? `
                        <button class="btn-icon ${isApproved ? 'unapprove-student' : 'approve-student'}"
                                data-student-id="${student.id}"
                                title="${isApproved ? 'إلغاء الاعتماد' : 'اعتماد'}">
                            <i class="fas ${isApproved ? 'fa-times' : 'fa-check'}"></i>
                        </button>
                        <button class="btn-icon view-details" data-student-id="${student.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                    ` : `
                        <span class="text-muted">بيانات ناقصة</span>
                    `}
                </td>
            </tr>
        `;
    }

    setupTableEventListeners() {
        // Individual approval buttons
        document.querySelectorAll('.approve-student').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const studentId = e.target.closest('button').dataset.studentId;
                this.approveStudent(studentId);
            });
        });

        document.querySelectorAll('.unapprove-student').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const studentId = e.target.closest('button').dataset.studentId;
                this.unapproveStudent(studentId);
            });
        });

        // View details buttons
        document.querySelectorAll('.view-details').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const studentId = e.target.closest('button').dataset.studentId;
                this.viewStudentDetails(studentId);
            });
        });

        // Checkbox change listeners
        document.querySelectorAll('.student-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectionButtons();
            });
        });
    }

    updateSelectionButtons() {
        const checkboxes = document.querySelectorAll('.student-checkbox:not(:disabled)');
        const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');

        const approveBtn = document.getElementById('approveSelectedBtn');
        const unapproveBtn = document.getElementById('unapproveSelectedBtn');

        if (approveBtn) approveBtn.disabled = checkedBoxes.length === 0;
        if (unapproveBtn) unapproveBtn.disabled = checkedBoxes.length === 0;
    }

    toggleSelectAll(checked) {
        document.querySelectorAll('.student-checkbox:not(:disabled)').forEach(checkbox => {
            checkbox.checked = checked;
        });
        this.updateSelectionButtons();
    }

    async approveStudent(studentId) {
        try {
            const currentYear = this.currentFilters.academicYear || window.app?.settings?.currentAcademicYear || '2024-2025';

            const approval = {
                id: Utils.generateId(),
                studentId: studentId,
                academicYear: currentYear,
                approvalDate: new Date().toISOString(),
                approvedBy: 'النظام', // Can be enhanced to include user info
                status: 'approved'
            };

            await db.addApproval(approval);
            Utils.showNotification('تم اعتماد الطالب بنجاح', 'success');

            // Refresh data
            await this.loadData();
            this.loadApprovalsData();

        } catch (error) {
            console.error('Error approving student:', error);
            Utils.showNotification('خطأ في اعتماد الطالب', 'error');
        }
    }

    async unapproveStudent(studentId) {
        try {
            const currentYear = this.currentFilters.academicYear || window.app?.settings?.currentAcademicYear || '2024-2025';

            await db.removeApproval(studentId, currentYear);
            Utils.showNotification('تم إلغاء اعتماد الطالب', 'success');

            // Refresh data
            await this.loadData();
            this.loadApprovalsData();

        } catch (error) {
            console.error('Error unapproving student:', error);
            Utils.showNotification('خطأ في إلغاء اعتماد الطالب', 'error');
        }
    }

    async approveSelected() {
        const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
        if (checkedBoxes.length === 0) return;

        if (confirm(`هل أنت متأكد من اعتماد ${checkedBoxes.length} طالب؟`)) {
            try {
                const promises = Array.from(checkedBoxes).map(checkbox =>
                    this.approveStudent(checkbox.value)
                );

                await Promise.all(promises);
                Utils.showNotification(`تم اعتماد ${checkedBoxes.length} طالب بنجاح`, 'success');

            } catch (error) {
                console.error('Error approving selected students:', error);
                Utils.showNotification('خطأ في اعتماد الطلاب المحددين', 'error');
            }
        }
    }

    async unapproveSelected() {
        const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
        if (checkedBoxes.length === 0) return;

        if (confirm(`هل أنت متأكد من إلغاء اعتماد ${checkedBoxes.length} طالب؟`)) {
            try {
                const promises = Array.from(checkedBoxes).map(checkbox =>
                    this.unapproveStudent(checkbox.value)
                );

                await Promise.all(promises);
                Utils.showNotification(`تم إلغاء اعتماد ${checkedBoxes.length} طالب بنجاح`, 'success');

            } catch (error) {
                console.error('Error unapproving selected students:', error);
                Utils.showNotification('خطأ في إلغاء اعتماد الطلاب المحددين', 'error');
            }
        }
    }

    async approveCurrentGrade() {
        if (!this.currentFilters.school || !this.currentFilters.grade) {
            Utils.showNotification('يرجى اختيار المدرسة والصف أولاً', 'error');
            return;
        }

        const message = `هل أنت متأكد من اعتماد جميع طلاب الصف ${this.getGradeName(this.currentFilters.grade)} في المدرسة المحددة؟`;

        if (confirm(message)) {
            try {
                // Get all students in current grade that have complete data
                const rows = document.querySelectorAll('.approval-row:not(.incomplete)');
                const studentIds = Array.from(rows).map(row => row.dataset.studentId);

                const promises = studentIds.map(studentId => this.approveStudent(studentId));
                await Promise.all(promises);

                Utils.showNotification(`تم اعتماد ${studentIds.length} طالب في الصف`, 'success');

            } catch (error) {
                console.error('Error approving current grade:', error);
                Utils.showNotification('خطأ في اعتماد الصف', 'error');
            }
        }
    }

    getGradeName(grade) {
        const names = {
            1: 'الأول', 2: 'الثاني', 3: 'الثالث', 4: 'الرابع',
            5: 'الخامس', 6: 'السادس', 7: 'السابع', 8: 'الثامن',
            9: 'التاسع', 10: 'العاشر', 11: 'الحادي عشر', 12: 'الثاني عشر'
        };
        return names[grade] || grade.toString();
    }

    updateOverviewCards(studentResults = []) {
        const totalCount = studentResults.length;
        const approvedCount = studentResults.filter(r => r.isApproved).length;
        const pendingCount = studentResults.filter(r => r.hasCompleteData && !r.isApproved).length;
        const averagePercentage = totalCount > 0 ?
            studentResults.reduce((sum, r) => sum + r.percentage, 0) / totalCount : 0;

        document.getElementById('totalStudentsCount').textContent = totalCount;
        document.getElementById('approvedStudentsCount').textContent = approvedCount;
        document.getElementById('pendingStudentsCount').textContent = pendingCount;
        document.getElementById('averageGrade').textContent = `${averagePercentage.toFixed(1)}%`;
    }

    async generateApprovalReport() {
        try {
            if (!this.currentFilters.school || !this.currentFilters.grade) {
                Utils.showNotification('يرجى اختيار المدرسة والصف أولاً', 'error');
                return;
            }

            await this.loadApprovalsData();
            const studentResults = await this.getCurrentStudentResults();

            if (studentResults.length === 0) {
                Utils.showNotification('لا توجد بيانات لإنشاء التقرير', 'error');
                return;
            }

            const school = this.schools.find(s => s.id === this.currentFilters.school);
            const reportHTML = this.generateApprovalReportHTML(studentResults, school);

            // Display report
            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(reportHTML);
            reportWindow.document.close();

        } catch (error) {
            console.error('Error generating approval report:', error);
            Utils.showNotification('خطأ في إنشاء تقرير الاعتماد', 'error');
        }
    }

    generateApprovalReportHTML(studentResults, school) {
        const currentYear = this.currentFilters.academicYear || window.app?.settings?.currentAcademicYear || '2024-2025';
        const gradeName = this.getGradeName(parseInt(this.currentFilters.grade));
        const section = this.currentFilters.section || 'جميع الشعب';
        const currentDate = new Date().toLocaleDateString('ar-SA');

        const approvedStudents = studentResults.filter(r => r.isApproved);
        const totalStudents = studentResults.length;
        const averagePercentage = totalStudents > 0 ?
            studentResults.reduce((sum, r) => sum + r.percentage, 0) / totalStudents : 0;

        return `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>تقرير اعتماد نتائج العام الدراسي</title>
                <style>
                    body { font-family: 'Cairo', Arial, sans-serif; margin: 20px; }
                    .report-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                    .report-title { font-size: 24px; font-weight: bold; margin: 10px 0; }
                    .report-info { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
                    .info-group { background: #f8f9fa; padding: 15px; border-radius: 8px; }
                    .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    .table th { background: #f8f9fa; font-weight: bold; }
                    .approved { background: #d4edda; }
                    .pending { background: #fff3cd; }
                    .incomplete { background: #f8d7da; }
                    .summary { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; }
                    .report-footer { margin-top: 40px; page-break-inside: avoid; }
                    .staff-signatures { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin: 20px 0; }
                    .signature-section { text-align: center; padding: 15px; border: 1px solid #ddd; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="report-header">
                    <h1>وزارة التعليم - المملكة العربية السعودية</h1>
                    <h2 class="report-title">تقرير اعتماد نتائج العام الدراسي</h2>
                    <p>العام الدراسي: ${currentYear}</p>
                </div>

                <div class="report-info">
                    <div class="info-group">
                        <h3>بيانات المدرسة</h3>
                        <p><strong>اسم المدرسة:</strong> ${school?.name || 'غير محدد'}</p>
                        <p><strong>كود المدرسة:</strong> ${school?.code || 'غير محدد'}</p>
                        <p><strong>الصف:</strong> ${gradeName}</p>
                        <p><strong>الشعبة:</strong> ${section}</p>
                    </div>

                    <div class="info-group">
                        <h3>ملخص النتائج</h3>
                        <p><strong>إجمالي الطلاب:</strong> ${totalStudents}</p>
                        <p><strong>الطلاب المعتمدين:</strong> ${approvedStudents.length}</p>
                        <p><strong>متوسط النسبة:</strong> ${averagePercentage.toFixed(1)}%</p>
                        <p><strong>تاريخ التقرير:</strong> ${currentDate}</p>
                    </div>
                </div>

                <table class="table">
                    <thead>
                        <tr>
                            <th>م</th>
                            <th>اسم الطالب</th>
                            <th>رقم الطالب</th>
                            <th>الفصل الأول</th>
                            <th>الفصل الثاني</th>
                            <th>المجموع</th>
                            <th>المتوسط</th>
                            <th>النسبة</th>
                            <th>المستوى</th>
                            <th>التقدير</th>
                            <th>حالة الاعتماد</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${studentResults.map((result, index) => `
                            <tr class="${result.isApproved ? 'approved' : (result.hasCompleteData ? 'pending' : 'incomplete')}">
                                <td>${index + 1}</td>
                                <td>${result.student.name}</td>
                                <td>${result.student.studentId}</td>
                                <td>${result.semester1Total.toFixed(1)}</td>
                                <td>${result.semester2Total.toFixed(1)}</td>
                                <td>${result.finalTotal.toFixed(1)}</td>
                                <td>${result.average.toFixed(1)}</td>
                                <td>${result.percentage.toFixed(1)}%</td>
                                <td>${result.level}</td>
                                <td>${result.grade}</td>
                                <td>${result.isApproved ? 'معتمد' : (result.hasCompleteData ? 'في انتظار الاعتماد' : 'بيانات ناقصة')}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>

                <div class="summary">
                    <h3>ملخص الإحصائيات</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div><strong>نسبة النجاح:</strong> ${totalStudents > 0 ? ((studentResults.filter(r => r.percentage >= 50).length / totalStudents) * 100).toFixed(1) : 0}%</div>
                        <div><strong>نسبة الاعتماد:</strong> ${totalStudents > 0 ? ((approvedStudents.length / totalStudents) * 100).toFixed(1) : 0}%</div>
                        <div><strong>أعلى نسبة:</strong> ${Math.max(...studentResults.map(r => r.percentage)).toFixed(1)}%</div>
                        <div><strong>أقل نسبة:</strong> ${Math.min(...studentResults.map(r => r.percentage)).toFixed(1)}%</div>
                    </div>
                </div>

                <div class="report-footer">
                    <div class="staff-signatures">
                        <div class="signature-section">
                            <p><strong>معلم المادة:</strong></p>
                            <p>${school?.teacherName || '___________________'}</p>
                            <p>التوقيع: ___________________</p>
                        </div>
                        <div class="signature-section">
                            <p><strong>المشرف التربوي:</strong></p>
                            <p>${school?.supervisorName || '___________________'}</p>
                            <p>التوقيع: ___________________</p>
                        </div>
                        <div class="signature-section">
                            <p><strong>مدير المدرسة:</strong></p>
                            <p>${school?.principalName || '___________________'}</p>
                            <p>التوقيع: ___________________</p>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
                        تم إنشاء هذا التقرير بواسطة نظام تقويم تقنية المعلومات
                    </div>
                </div>

                <script>
                    window.onload = function() {
                        window.print();
                    }
                </script>
            </body>
            </html>
        `;
    }

    async getCurrentStudentResults() {
        this.updateFilters();

        let filteredStudents = this.students.filter(student => {
            if (this.currentFilters.school && student.schoolId !== this.currentFilters.school) return false;
            if (this.currentFilters.grade && student.grade.toString() !== this.currentFilters.grade) return false;
            if (this.currentFilters.section && student.section !== this.currentFilters.section) return false;
            return true;
        });

        return await Promise.all(filteredStudents.map(async (student) => {
            return await this.calculateStudentFinalResult(student);
        }));
    }

    async exportApprovalData() {
        try {
            const studentResults = await this.getCurrentStudentResults();

            if (studentResults.length === 0) {
                Utils.showNotification('لا توجد بيانات للتصدير', 'error');
                return;
            }

            const data = studentResults.map((result, index) => ({
                'الرقم': index + 1,
                'اسم الطالب': result.student.name,
                'رقم الطالب': result.student.studentId,
                'المدرسة': this.schools.find(s => s.id === result.student.schoolId)?.name || 'غير محدد',
                'الصف': this.getGradeName(result.student.grade),
                'الشعبة': result.student.section,
                'الفصل الأول': result.semester1Total.toFixed(1),
                'الفصل الثاني': result.semester2Total.toFixed(1),
                'المجموع': result.finalTotal.toFixed(1),
                'المتوسط': result.average.toFixed(1),
                'النسبة': result.percentage.toFixed(1) + '%',
                'المستوى': result.level,
                'التقدير': result.grade,
                'حالة الاعتماد': result.isApproved ? 'معتمد' : (result.hasCompleteData ? 'في انتظار الاعتماد' : 'بيانات ناقصة')
            }));

            Utils.exportToExcel(data, `اعتمادات_${this.currentFilters.academicYear || '2024-2025'}`);

        } catch (error) {
            console.error('Error exporting approval data:', error);
            Utils.showNotification('خطأ في تصدير البيانات', 'error');
        }
    }

    async exportApprovedResults() {
        try {
            const allApprovals = this.approvals;

            if (allApprovals.length === 0) {
                Utils.showNotification('لا توجد نتائج معتمدة للتصدير', 'error');
                return;
            }

            const approvedData = [];

            for (const approval of allApprovals) {
                const student = this.students.find(s => s.id === approval.studentId);
                if (student) {
                    const result = await this.calculateStudentFinalResult(student);
                    const school = this.schools.find(s => s.id === student.schoolId);

                    approvedData.push({
                        'اسم الطالب': student.name,
                        'رقم الطالب': student.studentId,
                        'المدرسة': school?.name || 'غير محدد',
                        'الصف': this.getGradeName(student.grade),
                        'الشعبة': student.section,
                        'العام الدراسي': approval.academicYear,
                        'المجموع النهائي': result.finalTotal.toFixed(1),
                        'النسبة': result.percentage.toFixed(1) + '%',
                        'المستوى': result.level,
                        'التقدير': result.grade,
                        'تاريخ الاعتماد': new Date(approval.approvalDate).toLocaleDateString('ar-SA')
                    });
                }
            }

            Utils.exportToExcel(approvedData, `النتائج_المعتمدة_${new Date().getFullYear()}`);

        } catch (error) {
            console.error('Error exporting approved results:', error);
            Utils.showNotification('خطأ في تصدير النتائج المعتمدة', 'error');
        }
    }

    viewStudentDetails(studentId) {
        // This can be enhanced to show detailed breakdown
        const student = this.students.find(s => s.id === studentId);
        if (student) {
            Utils.showNotification(`عرض تفاصيل الطالب: ${student.name}`, 'info');
            // Can open a modal with detailed grade breakdown
        }
    }

    async approveAllGrades() {
        if (confirm('هل أنت متأكد من اعتماد جميع الصفوف في جميع المدارس؟ هذا الإجراء لا يمكن التراجع عنه بسهولة.')) {
            try {
                // Get all students with complete data
                const allStudentResults = [];

                for (const student of this.students) {
                    const result = await this.calculateStudentFinalResult(student);
                    if (result.hasCompleteData && !result.isApproved) {
                        allStudentResults.push(result);
                    }
                }

                if (allStudentResults.length === 0) {
                    Utils.showNotification('لا توجد طلاب جاهزين للاعتماد', 'info');
                    return;
                }

                const promises = allStudentResults.map(result => this.approveStudent(result.student.id));
                await Promise.all(promises);

                Utils.showNotification(`تم اعتماد ${allStudentResults.length} طالب في جميع الصفوف`, 'success');

                // Refresh data
                await this.loadData();
                this.loadApprovalsData();

            } catch (error) {
                console.error('Error approving all grades:', error);
                Utils.showNotification('خطأ في اعتماد جميع الصفوف', 'error');
            }
        }
    }
}

// Create global approvals manager instance
const approvalsManager = new ApprovalsManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize approvals manager when approvals section is first shown
    const approvalsSection = document.getElementById('approvals');
    if (approvalsSection) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (approvalsSection.classList.contains('active') && !approvalsManager.initialized) {
                        approvalsManager.initialize();
                    }
                }
            });
        });

        observer.observe(approvalsSection, { attributes: true });
    }
});

// Make approvalsManager available globally
window.approvalsManager = approvalsManager;
