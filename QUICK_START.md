# دليل البدء السريع - استمارة تقويم تقنية المعلومات

## التشغيل السريع (بدون تثبيت)

### الطريقة الأولى: فتح مباشر في المتصفح
1. حمّل الملفات أو استنسخ المشروع
2. افتح ملف `index.html` في أي متصفح حديث
3. ابدأ الاستخدام فوراً!

### الطريقة الثانية: خادم محلي بسيط
```bash
# إذا كان لديك Python مثبت
python -m http.server 8000

# أو إذا كان لديك Node.js
npx serve .

# ثم افتح http://localhost:8000 في المتصفح
```

## التشغيل كتطبيق سطح مكتب

### 1. تثبيت Node.js
- حمّل وثبّت Node.js من: https://nodejs.org
- اختر النسخة LTS (الموصى بها)

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تشغيل التطبيق
```bash
# للتطوير (مع أدوات المطور)
npm run dev

# للاستخدام العادي
npm start
```

### 4. بناء التطبيق للتوزيع
```bash
# لنظام Windows
npm run build-win

# لنظام macOS  
npm run build-mac

# لنظام Linux
npm run build-linux
```

## الخطوات الأولى

### 1. إضافة مدرسة
1. اضغط على "المدارس" في القائمة العلوية
2. اضغط "إضافة مدرسة"
3. أدخل:
   - اسم المدرسة
   - رمز المدرسة
   - العنوان (اختياري)
   - رقم الهاتف (اختياري)
4. احفظ البيانات

### 2. إضافة الطلاب

#### الطريقة الأولى: إدخال يدوي
1. اضغط على "الطلاب"
2. اضغط "إضافة طالب"
3. أدخل بيانات الطالب
4. احفظ

#### الطريقة الثانية: استيراد من Excel
1. اضغط على "الطلاب"
2. اضغط "استيراد من Excel"
3. حمّل نموذج Excel أولاً
4. املأ البيانات في النموذج
5. ارفع الملف

### 3. إدخال الدرجات
1. اضغط على "الدرجات"
2. اختر المدرسة من القائمة
3. اختر الصف الدراسي
4. اختر الشعبة
5. اختر الفصل الدراسي والعام الدراسي
6. أدخل الدرجات في الجدول
7. احفظ البيانات

### 4. عرض التقارير
1. اضغط على "التقارير"
2. اختر نوع التقرير:
   - تقرير فصلي
   - تقرير سنوي
   - تقرير الطلاب
   - تقرير الإحصائيات
3. حدد الفلاتر المطلوبة
4. اضغط "إنشاء التقرير"
5. اطبع أو صدّر النتائج

### 5. عرض الإحصائيات
1. اضغط على "الإحصائيات"
2. حدد الفلاتر (مدرسة، صف، فصل دراسي)
3. اضغط "تحديث الإحصائيات"
4. استعرض الرسوم البيانية والتحليلات

## نصائح مهمة

### حفظ البيانات
- البيانات تُحفظ تلقائياً في المتصفح (localStorage)
- في تطبيق سطح المكتب: تُحفظ في ملفات JSON
- انشئ نسخة احتياطية دورياً من قائمة "ملف"

### استيراد البيانات
- استخدم نموذج Excel المتوفر
- تأكد من ملء جميع الحقول المطلوبة
- الأعمدة المطلوبة: اسم الطالب، رقم الطالب، الصف، الشعبة

### نظام الدرجات
- **الصفوف 1-4**: المجموع من 50 درجة
- **الصفوف 5-12**: المجموع من 100 درجة
- المستويات تُحسب تلقائياً حسب المعايير المحددة

### الطباعة والتصدير
- جميع التقارير قابلة للطباعة
- يمكن تصدير البيانات إلى Excel
- استخدم وضع الطباعة في المتصفح للحصول على أفضل النتائج

## حل المشاكل الشائعة

### لا تظهر البيانات
```bash
# امسح ذاكرة التخزين المؤقت
# في المتصفح: Ctrl+Shift+Delete
# أو أعد تحميل الصفحة: F5
```

### خطأ في تشغيل Electron
```bash
# تأكد من تثبيت التبعيات
npm install

# امسح node_modules وأعد التثبيت
rm -rf node_modules
npm install
```

### مشاكل استيراد Excel
- تأكد من تنسيق الملف (.xlsx)
- استخدم النموذج المتوفر
- تحقق من وجود البيانات في الأعمدة الصحيحة

## اختصارات لوحة المفاتيح

- `Ctrl+S`: حفظ البيانات الحالية
- `Ctrl+B`: إنشاء نسخة احتياطية (في Electron)
- `F5`: إعادة تحميل
- `F11`: ملء الشاشة
- `Ctrl+Plus/Minus`: تكبير/تصغير
- `Escape`: إغلاق النوافذ المنبثقة

## الدعم

إذا واجهت أي مشاكل:
1. راجع ملف README.md للتفاصيل الكاملة
2. تحقق من Issues في GitHub
3. أنشئ Issue جديد إذا لم تجد الحل

---

**مبروك! أنت الآن جاهز لاستخدام نظام تقويم تقنية المعلومات** 🎉
