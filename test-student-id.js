// Quick test for automatic student ID generation
// Run this in browser console to test student ID functionality

console.log('🆔 Student ID Generation Test');

async function testStudentIdGeneration() {
    console.log('📋 Testing Student ID Generation...');
    
    try {
        // Test 1: Check if modal manager exists
        if (typeof window.modalManager === 'undefined') {
            console.log('❌ Modal manager not found');
            return false;
        }
        
        console.log('✅ Modal manager found');
        
        // Test 2: Test generateNextStudentId function
        if (typeof window.modalManager.generateNextStudentId === 'function') {
            console.log('🔄 Testing student ID generation...');
            
            const newId = await window.modalManager.generateNextStudentId();
            console.log('✅ Generated student ID:', newId);
            
            // Validate format (should be 6 digits: YY + 4 digits)
            const idPattern = /^\d{6}$/;
            if (idPattern.test(newId)) {
                console.log('✅ Student ID format is valid');
                
                // Extract year and number
                const year = newId.substring(0, 2);
                const number = newId.substring(2);
                console.log(`📅 Year: 20${year}, Number: ${number}`);
            } else {
                console.log('❌ Student ID format is invalid');
            }
            
            // Test multiple generations
            console.log('🔄 Testing multiple ID generations...');
            const ids = [];
            for (let i = 0; i < 5; i++) {
                const id = await window.modalManager.generateNextStudentId();
                ids.push(id);
            }
            
            console.log('📊 Generated IDs:', ids);
            
            // Check for duplicates
            const uniqueIds = [...new Set(ids)];
            if (uniqueIds.length === ids.length) {
                console.log('✅ All generated IDs are unique');
            } else {
                console.log('❌ Duplicate IDs found');
            }
            
        } else {
            console.log('❌ generateNextStudentId function not found');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Error testing student ID generation:', error);
        return false;
    }
}

// Test student form functionality
function testStudentForm() {
    console.log('📝 Testing Student Form...');
    
    // Open add student modal
    if (window.modalManager && window.modalManager.openAddStudentModal) {
        console.log('🔄 Opening add student modal...');
        window.modalManager.openAddStudentModal();
        
        setTimeout(() => {
            // Check if modal opened
            const modal = document.querySelector('.modal');
            if (modal && modal.style.display !== 'none') {
                console.log('✅ Add student modal opened');
                
                // Check for student ID field
                const studentIdInput = document.getElementById('studentId');
                if (studentIdInput) {
                    console.log('✅ Student ID input found');
                    console.log('📊 Current student ID value:', studentIdInput.value);
                    
                    // Check if field is readonly
                    if (studentIdInput.readOnly) {
                        console.log('✅ Student ID field is readonly (as expected)');
                    } else {
                        console.log('❌ Student ID field should be readonly');
                    }
                    
                    // Check for refresh button
                    const refreshBtn = document.querySelector('.refresh-student-id');
                    if (refreshBtn) {
                        console.log('✅ Refresh student ID button found');
                        
                        // Test refresh functionality
                        console.log('🔄 Testing refresh button...');
                        refreshBtn.click();
                        
                        setTimeout(() => {
                            console.log('📊 New student ID value:', studentIdInput.value);
                        }, 1000);
                    } else {
                        console.log('❌ Refresh student ID button not found');
                    }
                } else {
                    console.log('❌ Student ID input not found');
                }
                
                // Check that birth date field is removed
                const birthDateInput = document.getElementById('studentBirthDate');
                if (!birthDateInput) {
                    console.log('✅ Birth date field successfully removed');
                } else {
                    console.log('❌ Birth date field still exists');
                }
                
                // Check form fields
                const requiredFields = ['studentName', 'studentSchool', 'studentGrade', 'studentSection'];
                requiredFields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    const status = field ? '✅' : '❌';
                    console.log(`${status} Field ${fieldId}: ${field ? 'found' : 'not found'}`);
                });
                
            } else {
                console.log('❌ Add student modal did not open');
            }
        }, 1000);
    } else {
        console.log('❌ openAddStudentModal function not found');
    }
}

// Test student validation
function testStudentValidation() {
    console.log('✅ Testing Student Validation...');
    
    if (typeof Utils !== 'undefined' && Utils.validateStudent) {
        // Test valid student
        const validStudent = {
            name: 'أحمد محمد علي',
            studentId: '240001',
            schoolId: 'school-1',
            grade: 5,
            section: 'أ',
            notes: ''
        };
        
        const validErrors = Utils.validateStudent(validStudent);
        console.log('✅ Valid student errors:', validErrors.length === 0 ? 'None (correct)' : validErrors);
        
        // Test invalid student ID format
        const invalidStudent = {
            name: 'طالب تجريبي',
            studentId: '123', // Invalid format
            schoolId: 'school-1',
            grade: 5,
            section: 'أ',
            notes: ''
        };
        
        const invalidErrors = Utils.validateStudent(invalidStudent);
        console.log('❌ Invalid student errors:', invalidErrors);
        
        // Test missing required fields
        const incompleteStudent = {
            name: '',
            studentId: '',
            schoolId: '',
            grade: 0,
            section: '',
            notes: ''
        };
        
        const incompleteErrors = Utils.validateStudent(incompleteStudent);
        console.log('❌ Incomplete student errors:', incompleteErrors);
        
    } else {
        console.log('❌ Utils.validateStudent not found');
    }
}

// Test existing students to check ID format
async function testExistingStudents() {
    console.log('👥 Testing Existing Students...');
    
    try {
        const students = await db.getStudents();
        console.log(`📊 Found ${students.length} existing students`);
        
        if (students.length > 0) {
            console.log('📋 Student ID formats:');
            students.forEach((student, index) => {
                const idPattern = /^\d{6}$/;
                const isValid = idPattern.test(student.studentId);
                const status = isValid ? '✅' : '❌';
                console.log(`${status} ${student.name}: ${student.studentId} (${isValid ? 'valid' : 'invalid'} format)`);
                
                if (index < 5) { // Show first 5 only
                    console.log(`   Name: ${student.name}, Grade: ${student.grade}, Section: ${student.section}`);
                }
            });
            
            // Check for duplicates
            const ids = students.map(s => s.studentId);
            const uniqueIds = [...new Set(ids)];
            if (uniqueIds.length === ids.length) {
                console.log('✅ No duplicate student IDs found');
            } else {
                console.log('❌ Duplicate student IDs found');
                const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index);
                console.log('🔄 Duplicates:', [...new Set(duplicates)]);
            }
        }
        
    } catch (error) {
        console.error('❌ Error loading existing students:', error);
    }
}

// Run comprehensive test
async function runStudentIdTest() {
    console.log('🚀 Running Comprehensive Student ID Test...');
    console.log('='.repeat(50));
    
    await testStudentIdGeneration();
    
    setTimeout(() => {
        testStudentForm();
    }, 1000);
    
    setTimeout(() => {
        testStudentValidation();
    }, 2000);
    
    setTimeout(async () => {
        await testExistingStudents();
        console.log('='.repeat(50));
        console.log('✅ Student ID test completed!');
    }, 3000);
}

// Make functions available globally
window.testStudentIdGeneration = testStudentIdGeneration;
window.testStudentForm = testStudentForm;
window.testStudentValidation = testStudentValidation;
window.testExistingStudents = testExistingStudents;
window.runStudentIdTest = runStudentIdTest;

// Auto-run basic test
console.log(`
🆔 Student ID Test Commands Available:
- testStudentIdGeneration() - Test ID generation logic
- testStudentForm() - Test student form UI
- testStudentValidation() - Test validation rules
- testExistingStudents() - Check existing student IDs
- runStudentIdTest() - Run all tests

Quick start: runStudentIdTest()
`);

// Auto-run if students section is active
if (document.getElementById('students')?.classList.contains('active')) {
    console.log('🔄 Students section is active, running basic test...');
    testStudentIdGeneration();
}
