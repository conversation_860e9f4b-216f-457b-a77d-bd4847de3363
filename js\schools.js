// Schools management system

class SchoolsManager {
    constructor() {
        this.schools = [];
        this.filteredSchools = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Add school button
        const addSchoolBtn = document.getElementById('addSchoolBtn');
        if (addSchoolBtn) {
            addSchoolBtn.addEventListener('click', () => {
                modalManager.openAddSchoolModal();
            });
        }

        // Search functionality
        const searchInput = document.getElementById('searchSchools');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.filterSchools(e.target.value);
            }, 300));
        }
    }

    async loadSchools() {
        try {
            const loading = Utils.showLoading(document.getElementById('schoolsList'));
            
            this.schools = await db.getSchools();
            this.filteredSchools = [...this.schools];
            this.renderSchools();
            
            Utils.hideLoading(loading);
        } catch (error) {
            console.error('Error loading schools:', error);
            Utils.showNotification('خطأ في تحميل المدارس', 'error');
        }
    }

    filterSchools(searchTerm) {
        if (!searchTerm.trim()) {
            this.filteredSchools = [...this.schools];
        } else {
            this.filteredSchools = this.schools.filter(school =>
                school.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                school.code.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }
        this.currentPage = 1;
        this.renderSchools();
    }

    renderSchools() {
        const container = document.getElementById('schoolsList');
        if (!container) return;

        if (this.filteredSchools.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-school fa-3x"></i>
                    <h3>لا توجد مدارس</h3>
                    <p>ابدأ بإضافة مدرسة جديدة</p>
                    <button class="btn btn-primary" onclick="modalManager.openAddSchoolModal()">
                        <i class="fas fa-plus"></i> إضافة مدرسة
                    </button>
                </div>
            `;
            return;
        }

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedSchools = this.filteredSchools.slice(startIndex, endIndex);

        // Render schools grid
        const schoolsHTML = paginatedSchools.map(school => this.renderSchoolCard(school)).join('');
        
        container.innerHTML = `
            <div class="schools-grid">
                ${schoolsHTML}
            </div>
            ${this.renderPagination()}
        `;

        // Add event listeners for school actions
        this.addSchoolEventListeners();
    }

    renderSchoolCard(school) {
        const studentsCount = this.getSchoolStudentsCount(school.id);
        const createdDate = Utils.formatDateArabic(school.createdAt);

        return `
            <div class="school-card" data-school-id="${school.id}">
                <div class="school-header">
                    <div class="school-info">
                        <h3 class="school-name">${school.name}</h3>
                        <span class="school-code">${school.code}</span>
                    </div>
                    <div class="school-actions">
                        <button class="btn-icon edit-school" data-school-id="${school.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon delete-school" data-school-id="${school.id}" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="school-body">
                    ${school.address ? `
                        <div class="school-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>${school.address}</span>
                        </div>
                    ` : ''}
                    ${school.phone ? `
                        <div class="school-detail">
                            <i class="fas fa-phone"></i>
                            <span>${school.phone}</span>
                        </div>
                    ` : ''}
                    ${school.email ? `
                        <div class="school-detail">
                            <i class="fas fa-envelope"></i>
                            <span>${school.email}</span>
                        </div>
                    ` : ''}
                </div>
                <div class="school-footer">
                    <div class="school-stats">
                        <div class="stat">
                            <i class="fas fa-users"></i>
                            <span>${studentsCount} طالب</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-calendar"></i>
                            <span>${createdDate}</span>
                        </div>
                    </div>
                    <div class="school-quick-actions">
                        <button class="btn btn-sm btn-primary view-students" data-school-id="${school.id}">
                            <i class="fas fa-users"></i> عرض الطلاب
                        </button>
                        <button class="btn btn-sm btn-secondary view-reports" data-school-id="${school.id}">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    renderPagination() {
        const totalPages = Math.ceil(this.filteredSchools.length / this.itemsPerPage);
        if (totalPages <= 1) return '';

        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `
                <button class="pagination-btn" onclick="schoolsManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<span class="pagination-current">${i}</span>`;
            } else {
                paginationHTML += `
                    <button class="pagination-btn" onclick="schoolsManager.goToPage(${i})">${i}</button>
                `;
            }
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button class="pagination-btn" onclick="schoolsManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        return paginationHTML;
    }

    addSchoolEventListeners() {
        // Edit school buttons
        document.querySelectorAll('.edit-school').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const schoolId = e.currentTarget.dataset.schoolId;
                const school = this.schools.find(s => s.id === schoolId);
                if (school) {
                    modalManager.openEditSchoolModal(school);
                }
            });
        });

        // Delete school buttons
        document.querySelectorAll('.delete-school').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const schoolId = e.currentTarget.dataset.schoolId;
                await this.deleteSchool(schoolId);
            });
        });

        // View students buttons
        document.querySelectorAll('.view-students').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const schoolId = e.currentTarget.dataset.schoolId;
                this.viewSchoolStudents(schoolId);
            });
        });

        // View reports buttons
        document.querySelectorAll('.view-reports').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const schoolId = e.currentTarget.dataset.schoolId;
                this.viewSchoolReports(schoolId);
            });
        });
    }

    async deleteSchool(schoolId) {
        const school = this.schools.find(s => s.id === schoolId);
        if (!school) return;

        // Check if school has students
        const studentsCount = this.getSchoolStudentsCount(schoolId);
        if (studentsCount > 0) {
            Utils.showNotification(`لا يمكن حذف المدرسة لأنها تحتوي على ${studentsCount} طالب`, 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف مدرسة "${school.name}"؟`)) {
            try {
                await db.deleteSchool(schoolId);
                Utils.showNotification('تم حذف المدرسة بنجاح', 'success');
                await this.loadSchools();
            } catch (error) {
                console.error('Error deleting school:', error);
                Utils.showNotification('خطأ في حذف المدرسة', 'error');
            }
        }
    }

    getSchoolStudentsCount(schoolId) {
        // This would be implemented when students are loaded
        // For now, return 0
        return 0;
    }

    viewSchoolStudents(schoolId) {
        // Switch to students section and filter by school
        const studentsNavBtn = document.querySelector('[data-section="students"]');
        if (studentsNavBtn) {
            studentsNavBtn.click();
            
            // Set school filter after a short delay to ensure the section is loaded
            setTimeout(() => {
                const schoolFilter = document.getElementById('schoolFilter');
                if (schoolFilter) {
                    schoolFilter.value = schoolId;
                    schoolFilter.dispatchEvent(new Event('change'));
                }
            }, 100);
        }
    }

    viewSchoolReports(schoolId) {
        // Switch to reports section and filter by school
        const reportsNavBtn = document.querySelector('[data-section="reports"]');
        if (reportsNavBtn) {
            reportsNavBtn.click();
            
            // Set school filter after a short delay to ensure the section is loaded
            setTimeout(() => {
                const schoolFilter = document.getElementById('reportsSchoolFilter');
                if (schoolFilter) {
                    schoolFilter.value = schoolId;
                    schoolFilter.dispatchEvent(new Event('change'));
                }
            }, 100);
        }
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderSchools();
    }

    // Export schools data
    async exportSchools() {
        try {
            const schools = await db.getSchools();
            const exportData = schools.map(school => ({
                'اسم المدرسة': school.name,
                'رمز المدرسة': school.code,
                'العنوان': school.address || '',
                'الهاتف': school.phone || '',
                'البريد الإلكتروني': school.email || '',
                'تاريخ الإنشاء': Utils.formatDateArabic(school.createdAt),
                'ملاحظات': school.notes || ''
            }));

            Utils.exportToExcel(exportData, `المدارس_${new Date().toISOString().split('T')[0]}.xlsx`);
            Utils.showNotification('تم تصدير بيانات المدارس بنجاح', 'success');
        } catch (error) {
            console.error('Error exporting schools:', error);
            Utils.showNotification('خطأ في تصدير البيانات', 'error');
        }
    }

    // Search schools
    async searchSchools(query) {
        this.filterSchools(query);
    }

    // Get school by ID
    getSchoolById(schoolId) {
        return this.schools.find(school => school.id === schoolId);
    }

    // Get school name by ID
    getSchoolName(schoolId) {
        const school = this.getSchoolById(schoolId);
        return school ? school.name : 'غير محدد';
    }

    // Initialize schools section
    async initialize() {
        await this.loadSchools();
        
        // Add search functionality
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.id = 'searchSchools';
        searchInput.className = 'form-input';
        searchInput.placeholder = 'البحث عن مدرسة...';
        
        const sectionHeader = document.querySelector('#schools .section-header');
        if (sectionHeader) {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'search-container';
            searchContainer.appendChild(searchInput);
            sectionHeader.appendChild(searchContainer);
            
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.filterSchools(e.target.value);
            }, 300));
        }

        // Add export button
        const exportBtn = document.createElement('button');
        exportBtn.className = 'btn btn-secondary';
        exportBtn.innerHTML = '<i class="fas fa-download"></i> تصدير';
        exportBtn.addEventListener('click', () => this.exportSchools());
        
        const sectionActions = sectionHeader?.querySelector('.section-actions');
        if (sectionActions) {
            sectionActions.appendChild(exportBtn);
        }
    }
}

// Create global schools manager instance
const schoolsManager = new SchoolsManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize schools manager when schools section is first shown
    const schoolsSection = document.getElementById('schools');
    if (schoolsSection) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (schoolsSection.classList.contains('active') && !schoolsManager.initialized) {
                        schoolsManager.initialize();
                        schoolsManager.initialized = true;
                    }
                }
            });
        });
        
        observer.observe(schoolsSection, { attributes: true });
    }
});

// Make schoolsManager available globally
window.schoolsManager = schoolsManager;
