// Main application controller

class App {
    constructor() {
        this.currentSection = 'dashboard';
        this.initialized = false;
        this.managers = {};
        this.settings = null;
        this.initializeApp();
    }

    async initializeApp() {
        try {
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.init());
            } else {
                await this.init();
            }
        } catch (error) {
            console.error('Error initializing app:', error);
            Utils.showNotification('خطأ في تشغيل التطبيق', 'error');
        }
    }

    async init() {
        try {
            // Initialize database
            await this.initializeDatabase();

            // Load settings
            await this.loadSettings();

            // Setup navigation
            this.setupNavigation();

            // Load dashboard data
            await this.loadDashboard();

            // Setup global event listeners
            this.setupGlobalEventListeners();

            // Mark as initialized
            this.initialized = true;

            console.log('Application initialized successfully');

        } catch (error) {
            console.error('Error during app initialization:', error);
            Utils.showNotification('خطأ في تهيئة التطبيق', 'error');
        }
    }

    async loadSettings() {
        try {
            this.settings = await db.getSettings();
            if (!this.settings) {
                // Use default settings if none exist
                this.settings = {
                    currentAcademicYear: '2024-2025',
                    currentSemester: 1,
                    language: 'ar',
                    theme: 'light'
                };
            }
            console.log('Settings loaded:', this.settings);
        } catch (error) {
            console.error('Error loading settings:', error);
            this.settings = {
                currentAcademicYear: '2024-2025',
                currentSemester: 1,
                language: 'ar',
                theme: 'light'
            };
        }
    }

    async initializeDatabase() {
        try {
            // Database is already initialized in database.js
            // Just verify it's working
            const schools = await db.getSchools();
            const students = await db.getStudents();
            const grades = await db.getGrades();
            
            console.log(`Database initialized: ${schools.length} schools, ${students.length} students, ${grades.length} grade records`);
        } catch (error) {
            console.error('Database initialization error:', error);
            throw error;
        }
    }

    setupNavigation() {
        const navButtons = document.querySelectorAll('.nav-btn');
        
        navButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const section = btn.dataset.section;
                this.navigateToSection(section);
            });
        });
    }

    navigateToSection(sectionName) {
        // Update navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(sectionName).classList.add('active');

        // Update current section
        this.currentSection = sectionName;

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    async loadSectionData(sectionName) {
        try {
            switch (sectionName) {
                case 'dashboard':
                    await this.loadDashboard();
                    break;
                case 'schools':
                    // Schools manager will auto-initialize when section becomes active
                    break;
                case 'students':
                    // Students manager will auto-initialize when section becomes active
                    break;
                case 'grades':
                    // Grades manager will auto-initialize when section becomes active
                    break;
                case 'reports':
                    // Reports manager will auto-initialize when section becomes active
                    break;
                case 'statistics':
                    // Statistics manager will auto-initialize when section becomes active
                    break;
            }
        } catch (error) {
            console.error(`Error loading section ${sectionName}:`, error);
            Utils.showNotification(`خطأ في تحميل قسم ${this.getSectionName(sectionName)}`, 'error');
        }
    }

    async loadDashboard() {
        try {
            // Get counts from database
            const [schools, students, grades] = await Promise.all([
                db.getSchools(),
                db.getStudents(),
                db.getGrades()
            ]);

            // Update dashboard cards
            document.getElementById('schoolsCount').textContent = schools.length;
            document.getElementById('studentsCount').textContent = students.length;
            document.getElementById('gradesCount').textContent = grades.length;

            // Update current year
            const settings = await db.getSettings();
            document.getElementById('currentYear').textContent = settings.currentAcademicYear || '2024-2025';

        } catch (error) {
            console.error('Error loading dashboard:', error);
            Utils.showNotification('خطأ في تحميل لوحة التحكم', 'error');
        }
    }

    setupGlobalEventListeners() {
        // Handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S to save (prevent default browser save)
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.handleGlobalSave();
            }

            // Escape to close modals
            if (e.key === 'Escape') {
                if (modalManager && modalManager.overlay.classList.contains('active')) {
                    modalManager.closeModal();
                }
            }
        });

        // Handle window resize for responsive charts
        window.addEventListener('resize', Utils.debounce(() => {
            this.handleWindowResize();
        }, 250));

        // Handle online/offline status
        window.addEventListener('online', () => {
            Utils.showNotification('تم استعادة الاتصال بالإنترنت', 'success');
        });

        window.addEventListener('offline', () => {
            Utils.showNotification('تم فقدان الاتصال بالإنترنت - سيتم حفظ البيانات محلياً', 'warning');
        });

        // Handle before unload (warn about unsaved changes)
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = 'لديك تغييرات غير محفوظة. هل أنت متأكد من الخروج؟';
                return e.returnValue;
            }
        });
    }

    handleGlobalSave() {
        // Trigger save based on current section
        switch (this.currentSection) {
            case 'grades':
                if (window.gradesManager) {
                    window.gradesManager.saveAllGrades();
                }
                break;
            default:
                Utils.showNotification('لا توجد بيانات للحفظ في هذا القسم', 'info');
        }
    }

    handleWindowResize() {
        // Resize charts if statistics section is active
        if (this.currentSection === 'statistics' && window.statisticsManager) {
            // Charts will auto-resize due to responsive: true option
        }
    }

    hasUnsavedChanges() {
        // Check if there are any unsaved changes in forms
        const forms = document.querySelectorAll('form');
        for (const form of forms) {
            if (form.classList.contains('dirty')) {
                return true;
            }
        }

        // Check for unsaved grades
        const gradeInputs = document.querySelectorAll('.grade-input');
        for (const input of gradeInputs) {
            if (input.classList.contains('modified')) {
                return true;
            }
        }

        return false;
    }

    getSectionName(sectionKey) {
        const names = {
            dashboard: 'لوحة التحكم',
            schools: 'المدارس',
            students: 'الطلاب',
            grades: 'الدرجات',
            reports: 'التقارير',
            statistics: 'الإحصائيات'
        };
        return names[sectionKey] || sectionKey;
    }

    // Utility methods for other components
    async refreshDashboard() {
        if (this.currentSection === 'dashboard') {
            await this.loadDashboard();
        }
    }

    async refreshAllData() {
        try {
            // Reload settings first
            await this.loadSettings();

            // Refresh all managers
            if (window.schoolsManager && window.schoolsManager.initialized) {
                await window.schoolsManager.loadSchools();
            }

            if (window.studentsManager && window.studentsManager.initialized) {
                await window.studentsManager.loadStudents();
            }

            if (window.gradesManager && window.gradesManager.initialized) {
                await window.gradesManager.loadGradesTable();
            }

            if (window.reportsManager && window.reportsManager.initialized) {
                // Reports don't need refresh as they generate on demand
            }

            if (window.statisticsManager && window.statisticsManager.initialized) {
                await window.statisticsManager.updateStatistics();
            }

            if (window.settingsManager && window.settingsManager.initialized) {
                await window.settingsManager.loadSettings();
            }

            // Refresh dashboard
            await this.refreshDashboard();

            Utils.showNotification('تم تحديث جميع البيانات بنجاح', 'success');

        } catch (error) {
            console.error('Error refreshing data:', error);
            Utils.showNotification('خطأ في تحديث البيانات', 'error');
        }
    }

    // Backup and restore functionality
    async createBackup() {
        try {
            const backupPath = await db.createBackup();
            if (backupPath) {
                Utils.showNotification('تم إنشاء نسخة احتياطية بنجاح', 'success');
            } else {
                Utils.showNotification('النسخ الاحتياطي متاح فقط في تطبيق سطح المكتب', 'info');
            }
        } catch (error) {
            console.error('Error creating backup:', error);
            Utils.showNotification('خطأ في إنشاء النسخة الاحتياطية', 'error');
        }
    }

    // Theme management
    toggleTheme() {
        const body = document.body;
        const isDark = body.classList.contains('dark-theme');
        
        if (isDark) {
            body.classList.remove('dark-theme');
            localStorage.setItem('theme', 'light');
        } else {
            body.classList.add('dark-theme');
            localStorage.setItem('theme', 'dark');
        }
    }

    loadTheme() {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
        }
    }

    // Error handling
    handleError(error, context = '') {
        console.error(`Error in ${context}:`, error);
        
        let message = 'حدث خطأ غير متوقع';
        if (context) {
            message += ` في ${context}`;
        }
        
        Utils.showNotification(message, 'error');
    }

    // Performance monitoring
    startPerformanceMonitoring() {
        // Monitor page load time
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
        });

        // Monitor memory usage (if available)
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('High memory usage detected');
                }
            }, 30000); // Check every 30 seconds
        }
    }
}

// Initialize the application
const app = new App();

// Make app available globally for debugging
window.app = app;

// Add some global utility functions
window.refreshData = () => app.refreshAllData();
window.createBackup = () => app.createBackup();
window.toggleTheme = () => app.toggleTheme();

// Load theme on startup
document.addEventListener('DOMContentLoaded', () => {
    app.loadTheme();
    app.startPerformanceMonitoring();
});
