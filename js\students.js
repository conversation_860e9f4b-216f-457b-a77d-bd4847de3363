// Students management system

class StudentsManager {
    constructor() {
        this.students = [];
        this.schools = [];
        this.filteredStudents = [];
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.filters = {
            school: '',
            grade: '',
            section: '',
            search: ''
        };
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Add student button
        const addStudentBtn = document.getElementById('addStudentBtn');
        if (addStudentBtn) {
            addStudentBtn.addEventListener('click', () => {
                modalManager.openAddStudentModal();
            });
        }

        // Import students button
        const importStudentsBtn = document.getElementById('importStudentsBtn');
        if (importStudentsBtn) {
            importStudentsBtn.addEventListener('click', () => {
                modalManager.openImportStudentsModal();
            });
        }

        // Filter event listeners
        const schoolFilter = document.getElementById('schoolFilter');
        if (schoolFilter) {
            schoolFilter.addEventListener('change', (e) => {
                this.filters.school = e.target.value;
                this.applyFilters();
            });
        }

        const gradeFilter = document.getElementById('gradeFilter');
        if (gradeFilter) {
            gradeFilter.addEventListener('change', (e) => {
                this.filters.grade = e.target.value;
                this.applyFilters();
            });
        }

        const searchInput = document.getElementById('searchStudents');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.filters.search = e.target.value;
                this.applyFilters();
            }, 300));
        }
    }

    async loadStudents() {
        try {
            const loading = Utils.showLoading(document.getElementById('studentsTable'));
            
            [this.students, this.schools] = await Promise.all([
                db.getStudents(),
                db.getSchools()
            ]);
            
            this.filteredStudents = [...this.students];
            this.loadSchoolsFilter();
            this.renderStudents();
            
            Utils.hideLoading(loading);
        } catch (error) {
            console.error('Error loading students:', error);
            Utils.showNotification('خطأ في تحميل الطلاب', 'error');
        }
    }

    loadSchoolsFilter() {
        const schoolFilter = document.getElementById('schoolFilter');
        if (!schoolFilter) return;

        schoolFilter.innerHTML = '<option value="">جميع المدارس</option>';
        this.schools.forEach(school => {
            const option = document.createElement('option');
            option.value = school.id;
            option.textContent = school.name;
            schoolFilter.appendChild(option);
        });
    }

    applyFilters() {
        this.filteredStudents = this.students.filter(student => {
            // School filter
            if (this.filters.school && student.schoolId !== this.filters.school) {
                return false;
            }

            // Grade filter
            if (this.filters.grade && student.grade !== parseInt(this.filters.grade)) {
                return false;
            }

            // Search filter
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                return student.name.toLowerCase().includes(searchTerm) ||
                       student.studentId.toLowerCase().includes(searchTerm);
            }

            return true;
        });

        this.currentPage = 1;
        this.renderStudents();
    }

    renderStudents() {
        const container = document.getElementById('studentsTable');
        if (!container) return;

        if (this.filteredStudents.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users fa-3x"></i>
                    <h3>لا توجد طلاب</h3>
                    <p>ابدأ بإضافة طالب جديد أو استيراد قائمة من Excel</p>
                    <div class="empty-actions">
                        <button class="btn btn-primary" onclick="modalManager.openAddStudentModal()">
                            <i class="fas fa-user-plus"></i> إضافة طالب
                        </button>
                        <button class="btn btn-secondary" onclick="modalManager.openImportStudentsModal()">
                            <i class="fas fa-file-excel"></i> استيراد من Excel
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const paginatedStudents = this.filteredStudents.slice(startIndex, endIndex);

        // Render students table
        container.innerHTML = `
            <div class="table-container">
                <table class="table students-table">
                    <thead>
                        <tr>
                            <th>اسم الطالب</th>
                            <th>رقم الطالب</th>
                            <th>المدرسة</th>
                            <th>الصف</th>
                            <th>الشعبة</th>
                            <th>تاريخ الميلاد</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${paginatedStudents.map(student => this.renderStudentRow(student)).join('')}
                    </tbody>
                </table>
            </div>
            ${this.renderPagination()}
            <div class="table-info">
                <span>عرض ${startIndex + 1} إلى ${Math.min(endIndex, this.filteredStudents.length)} من ${this.filteredStudents.length} طالب</span>
            </div>
        `;

        // Add event listeners for student actions
        this.addStudentEventListeners();
    }

    renderStudentRow(student) {
        const school = this.schools.find(s => s.id === student.schoolId);
        const schoolName = school ? school.name : 'غير محدد';
        const birthDate = student.birthDate ? Utils.formatDateArabic(student.birthDate) : '';

        return `
            <tr data-student-id="${student.id}">
                <td class="student-name">${student.name}</td>
                <td class="student-id">${student.studentId}</td>
                <td>${schoolName}</td>
                <td class="student-grade">${this.getGradeName(student.grade)}</td>
                <td>${student.section}</td>
                <td>${birthDate}</td>
                <td class="action-buttons">
                    <button class="btn-icon edit-student" data-student-id="${student.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon view-grades" data-student-id="${student.id}" title="عرض الدرجات">
                        <i class="fas fa-chart-line"></i>
                    </button>
                    <button class="btn-icon delete-student" data-student-id="${student.id}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }

    renderPagination() {
        const totalPages = Math.ceil(this.filteredStudents.length / this.itemsPerPage);
        if (totalPages <= 1) return '';

        let paginationHTML = '<div class="pagination">';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `
                <button class="pagination-btn" onclick="studentsManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
        }

        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<button class="pagination-btn" onclick="studentsManager.goToPage(1)">1</button>`;
            if (startPage > 2) {
                paginationHTML += '<span class="pagination-dots">...</span>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<span class="pagination-current">${i}</span>`;
            } else {
                paginationHTML += `
                    <button class="pagination-btn" onclick="studentsManager.goToPage(${i})">${i}</button>
                `;
            }
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += '<span class="pagination-dots">...</span>';
            }
            paginationHTML += `<button class="pagination-btn" onclick="studentsManager.goToPage(${totalPages})">${totalPages}</button>`;
        }

        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button class="pagination-btn" onclick="studentsManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        paginationHTML += '</div>';
        return paginationHTML;
    }

    addStudentEventListeners() {
        // Edit student buttons
        document.querySelectorAll('.edit-student').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const studentId = e.currentTarget.dataset.studentId;
                const student = this.students.find(s => s.id === studentId);
                if (student) {
                    modalManager.openEditStudentModal(student);
                }
            });
        });

        // Delete student buttons
        document.querySelectorAll('.delete-student').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const studentId = e.currentTarget.dataset.studentId;
                await this.deleteStudent(studentId);
            });
        });

        // View grades buttons
        document.querySelectorAll('.view-grades').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const studentId = e.currentTarget.dataset.studentId;
                this.viewStudentGrades(studentId);
            });
        });
    }

    async deleteStudent(studentId) {
        const student = this.students.find(s => s.id === studentId);
        if (!student) return;

        if (confirm(`هل أنت متأكد من حذف الطالب "${student.name}"؟`)) {
            try {
                await db.deleteStudent(studentId);
                Utils.showNotification('تم حذف الطالب بنجاح', 'success');
                await this.loadStudents();
            } catch (error) {
                console.error('Error deleting student:', error);
                Utils.showNotification('خطأ في حذف الطالب', 'error');
            }
        }
    }

    viewStudentGrades(studentId) {
        // Switch to grades section and filter by student
        const gradesNavBtn = document.querySelector('[data-section="grades"]');
        if (gradesNavBtn) {
            gradesNavBtn.click();
            
            // Set student filter after a short delay to ensure the section is loaded
            setTimeout(() => {
                if (window.gradesManager) {
                    window.gradesManager.selectStudent(studentId);
                }
            }, 100);
        }
    }

    getGradeName(grade) {
        const names = {
            1: 'الأول', 2: 'الثاني', 3: 'الثالث', 4: 'الرابع',
            5: 'الخامس', 6: 'السادس', 7: 'السابع', 8: 'الثامن',
            9: 'التاسع', 10: 'العاشر', 11: 'الحادي عشر', 12: 'الثاني عشر'
        };
        return names[grade] || grade.toString();
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderStudents();
    }

    // Export students data
    async exportStudents() {
        try {
            const exportData = this.filteredStudents.map(student => {
                const school = this.schools.find(s => s.id === student.schoolId);
                return {
                    'اسم الطالب': student.name,
                    'رقم الطالب': student.studentId,
                    'المدرسة': school ? school.name : 'غير محدد',
                    'الصف الدراسي': this.getGradeName(student.grade),
                    'الشعبة': student.section,
                    'تاريخ الميلاد': student.birthDate || '',
                    'تاريخ التسجيل': Utils.formatDateArabic(student.createdAt),
                    'ملاحظات': student.notes || ''
                };
            });

            Utils.exportToExcel(exportData, `الطلاب_${new Date().toISOString().split('T')[0]}.xlsx`);
            Utils.showNotification('تم تصدير بيانات الطلاب بنجاح', 'success');
        } catch (error) {
            console.error('Error exporting students:', error);
            Utils.showNotification('خطأ في تصدير البيانات', 'error');
        }
    }

    // Get student by ID
    getStudentById(studentId) {
        return this.students.find(student => student.id === studentId);
    }

    // Get students by school
    getStudentsBySchool(schoolId) {
        return this.students.filter(student => student.schoolId === schoolId);
    }

    // Get students by grade
    getStudentsByGrade(grade) {
        return this.students.filter(student => student.grade === grade);
    }

    // Get students by school and grade
    getStudentsBySchoolAndGrade(schoolId, grade, section = null) {
        return this.students.filter(student => 
            student.schoolId === schoolId && 
            student.grade === grade &&
            (section === null || student.section === section)
        );
    }

    // Initialize students section
    async initialize() {
        await this.loadStudents();
        
        // Add export button
        const exportBtn = document.createElement('button');
        exportBtn.className = 'btn btn-secondary';
        exportBtn.innerHTML = '<i class="fas fa-download"></i> تصدير';
        exportBtn.addEventListener('click', () => this.exportStudents());
        
        const sectionActions = document.querySelector('#students .section-actions');
        if (sectionActions) {
            sectionActions.appendChild(exportBtn);
        }
    }

    // Update students count for schools
    updateSchoolStudentsCount() {
        if (window.schoolsManager) {
            window.schoolsManager.getSchoolStudentsCount = (schoolId) => {
                return this.students.filter(student => student.schoolId === schoolId).length;
            };
        }
    }
}

// Create global students manager instance
const studentsManager = new StudentsManager();

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize students manager when students section is first shown
    const studentsSection = document.getElementById('students');
    if (studentsSection) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (studentsSection.classList.contains('active') && !studentsManager.initialized) {
                        studentsManager.initialize();
                        studentsManager.initialized = true;
                    }
                }
            });
        });
        
        observer.observe(studentsSection, { attributes: true });
    }
});

// Make studentsManager available globally
window.studentsManager = studentsManager;
