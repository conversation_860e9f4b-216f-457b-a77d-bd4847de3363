// Quick test for staff information functionality
// Run this in browser console to test staff info features

console.log('👥 Staff Information Test');

async function testStaffInfo() {
    console.log('📋 Testing Staff Information...');
    
    try {
        // Test 1: Check if schools have staff information
        const schools = await db.getSchools();
        console.log(`📊 Found ${schools.length} schools`);
        
        if (schools.length > 0) {
            console.log('👥 Staff information in schools:');
            schools.forEach((school, index) => {
                console.log(`🏫 ${school.name}:`);
                console.log(`   👨‍🏫 معلم: ${school.teacherName || 'غير محدد'}`);
                console.log(`   👨‍💼 مشرف: ${school.supervisorName || 'غير محدد'}`);
                console.log(`   👨‍💻 مدير: ${school.principalName || 'غير محدد'}`);
                
                // Check if all fields are present
                const hasAllFields = school.teacherName && school.supervisorName && school.principalName;
                const status = hasAllFields ? '✅' : '❌';
                console.log(`   ${status} جميع الحقول: ${hasAllFields ? 'مكتملة' : 'ناقصة'}`);
                console.log('');
            });
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Error testing staff info:', error);
        return false;
    }
}

// Test school form functionality
function testSchoolForm() {
    console.log('📝 Testing School Form...');
    
    // Open add school modal
    if (window.modalManager && window.modalManager.openAddSchoolModal) {
        console.log('🔄 Opening add school modal...');
        window.modalManager.openAddSchoolModal();
        
        setTimeout(() => {
            // Check if modal opened
            const modal = document.querySelector('.modal');
            if (modal && modal.style.display !== 'none') {
                console.log('✅ Add school modal opened');
                
                // Check for staff information fields
                const staffFields = [
                    { id: 'teacherName', label: 'Teacher Name' },
                    { id: 'supervisorName', label: 'Supervisor Name' },
                    { id: 'principalName', label: 'Principal Name' }
                ];
                
                staffFields.forEach(field => {
                    const input = document.getElementById(field.id);
                    const status = input ? '✅' : '❌';
                    console.log(`${status} ${field.label} field: ${input ? 'found' : 'not found'}`);
                    
                    if (input) {
                        console.log(`   Placeholder: "${input.placeholder}"`);
                    }
                });
                
                // Check for form section
                const formSection = document.querySelector('.form-section');
                if (formSection) {
                    console.log('✅ Staff information section found');
                    
                    const sectionTitle = formSection.querySelector('.form-section-title');
                    if (sectionTitle) {
                        console.log(`   Section title: "${sectionTitle.textContent.trim()}"`);
                    }
                } else {
                    console.log('❌ Staff information section not found');
                }
                
                // Check form row layout
                const formRow = document.querySelector('.form-row');
                if (formRow) {
                    console.log('✅ Form row layout found');
                    const fieldsInRow = formRow.querySelectorAll('.form-group').length;
                    console.log(`   Fields in row: ${fieldsInRow}`);
                } else {
                    console.log('❌ Form row layout not found');
                }
                
            } else {
                console.log('❌ Add school modal did not open');
            }
        }, 1000);
    } else {
        console.log('❌ openAddSchoolModal function not found');
    }
}

// Test report footer functionality
async function testReportFooter() {
    console.log('📄 Testing Report Footer...');
    
    if (window.reportsManager && window.reportsManager.generateReportFooter) {
        const schools = await db.getSchools();
        
        if (schools.length > 0) {
            const testSchool = schools[0];
            console.log(`🏫 Testing with school: ${testSchool.name}`);
            
            const footerHTML = window.reportsManager.generateReportFooter(testSchool.id);
            
            if (footerHTML) {
                console.log('✅ Report footer generated successfully');
                
                // Check if footer contains staff names
                const hasTeacher = footerHTML.includes(testSchool.teacherName || '___________________');
                const hasSupervisor = footerHTML.includes(testSchool.supervisorName || '___________________');
                const hasPrincipal = footerHTML.includes(testSchool.principalName || '___________________');
                
                console.log(`   👨‍🏫 Teacher name: ${hasTeacher ? '✅' : '❌'} ${testSchool.teacherName || 'not set'}`);
                console.log(`   👨‍💼 Supervisor name: ${hasSupervisor ? '✅' : '❌'} ${testSchool.supervisorName || 'not set'}`);
                console.log(`   👨‍💻 Principal name: ${hasPrincipal ? '✅' : '❌'} ${testSchool.principalName || 'not set'}`);
                
                // Check for signature sections
                const signatureSections = (footerHTML.match(/signature-section/g) || []).length;
                console.log(`   📝 Signature sections: ${signatureSections} (expected: 3)`);
                
                // Check for current date
                const hasDate = footerHTML.includes('تاريخ إنشاء التقرير');
                console.log(`   📅 Date included: ${hasDate ? '✅' : '❌'}`);
                
            } else {
                console.log('❌ Report footer not generated');
            }
        } else {
            console.log('❌ No schools found for testing');
        }
    } else {
        console.log('❌ generateReportFooter function not found');
    }
}

// Test report generation with footer
async function testReportWithFooter() {
    console.log('📊 Testing Report Generation with Footer...');
    
    // Check if we're on the reports page
    const reportsSection = document.getElementById('reports');
    if (!reportsSection || !reportsSection.classList.contains('active')) {
        console.log('⚠️ Please navigate to the Reports section first');
        return;
    }
    
    if (window.reportsManager) {
        // Try to generate a semester report
        console.log('🔄 Generating test semester report...');
        
        try {
            await window.reportsManager.generateReport('semester');
            
            setTimeout(() => {
                const reportOutput = document.getElementById('reportOutput');
                if (reportOutput && reportOutput.innerHTML.trim() !== '') {
                    console.log('✅ Report generated successfully');
                    
                    // Check for report footer
                    const reportFooter = reportOutput.querySelector('.report-footer');
                    if (reportFooter) {
                        console.log('✅ Report footer found in generated report');
                        
                        // Check footer components
                        const staffSignatures = reportFooter.querySelector('.staff-signatures');
                        const reportMeta = reportFooter.querySelector('.report-meta');
                        
                        console.log(`   👥 Staff signatures: ${staffSignatures ? '✅' : '❌'}`);
                        console.log(`   📋 Report meta: ${reportMeta ? '✅' : '❌'}`);
                        
                        if (staffSignatures) {
                            const signatureSections = staffSignatures.querySelectorAll('.signature-section');
                            console.log(`   📝 Signature sections found: ${signatureSections.length}`);
                        }
                        
                    } else {
                        console.log('❌ Report footer not found in generated report');
                    }
                } else {
                    console.log('❌ Report not generated or empty');
                }
            }, 2000);
            
        } catch (error) {
            console.error('❌ Error generating report:', error);
        }
    } else {
        console.log('❌ Reports manager not found');
    }
}

// Test CSS styles for staff info
function testStaffInfoStyles() {
    console.log('🎨 Testing Staff Info CSS Styles...');
    
    // Check for form section styles
    const testElement = document.createElement('div');
    testElement.className = 'form-section';
    document.body.appendChild(testElement);
    
    const styles = window.getComputedStyle(testElement);
    const hasBackground = styles.backgroundColor !== 'rgba(0, 0, 0, 0)';
    const hasPadding = parseFloat(styles.padding) > 0;
    const hasBorder = styles.border !== 'none';
    
    console.log(`   📦 Form section background: ${hasBackground ? '✅' : '❌'}`);
    console.log(`   📏 Form section padding: ${hasPadding ? '✅' : '❌'}`);
    console.log(`   🔲 Form section border: ${hasBorder ? '✅' : '❌'}`);
    
    document.body.removeChild(testElement);
    
    // Check for report footer styles
    const footerElement = document.createElement('div');
    footerElement.className = 'report-footer';
    document.body.appendChild(footerElement);
    
    const footerStyles = window.getComputedStyle(footerElement);
    const hasMarginTop = parseFloat(footerStyles.marginTop) > 0;
    const hasBorderTop = footerStyles.borderTop !== 'none';
    
    console.log(`   📄 Report footer margin: ${hasMarginTop ? '✅' : '❌'}`);
    console.log(`   📄 Report footer border: ${hasBorderTop ? '✅' : '❌'}`);
    
    document.body.removeChild(footerElement);
}

// Run comprehensive test
async function runStaffInfoTest() {
    console.log('🚀 Running Comprehensive Staff Info Test...');
    console.log('='.repeat(50));
    
    await testStaffInfo();
    
    setTimeout(() => {
        testSchoolForm();
    }, 1000);
    
    setTimeout(async () => {
        await testReportFooter();
    }, 3000);
    
    setTimeout(() => {
        testStaffInfoStyles();
    }, 4000);
    
    setTimeout(async () => {
        await testReportWithFooter();
        console.log('='.repeat(50));
        console.log('✅ Staff info test completed!');
    }, 5000);
}

// Make functions available globally
window.testStaffInfo = testStaffInfo;
window.testSchoolForm = testSchoolForm;
window.testReportFooter = testReportFooter;
window.testReportWithFooter = testReportWithFooter;
window.testStaffInfoStyles = testStaffInfoStyles;
window.runStaffInfoTest = runStaffInfoTest;

// Auto-run basic test
console.log(`
👥 Staff Info Test Commands Available:
- testStaffInfo() - Test staff data in schools
- testSchoolForm() - Test school form UI
- testReportFooter() - Test report footer generation
- testReportWithFooter() - Test full report with footer
- testStaffInfoStyles() - Test CSS styles
- runStaffInfoTest() - Run all tests

Quick start: runStaffInfoTest()
`);

// Auto-run if schools section is active
if (document.getElementById('schools')?.classList.contains('active')) {
    console.log('🔄 Schools section is active, running basic test...');
    testStaffInfo();
}
