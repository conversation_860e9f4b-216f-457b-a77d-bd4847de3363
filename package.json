{"name": "it-evaluation-system", "version": "1.0.0", "description": "استمارة تقويم تقنية المعلومات - نظام شامل لإدارة درجات الطلاب في مادة تقنية المعلومات", "main": "electron/main.js", "homepage": "./", "author": {"name": "IT Evaluation System", "email": "<EMAIL>"}, "license": "MIT", "private": true, "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"@electron/remote": "^2.0.12"}, "build": {"appId": "com.itevaluation.app", "productName": "استمارة تقويم تقنية المعلومات", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "!dist/**/*", "!.git/**/*", "!README.md", "!.giti<PERSON>re"], "extraResources": [{"from": "data", "to": "data", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.education"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Education"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "استمارة تقويم تقنية المعلومات", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Education", "artifactName": "${productName}-${version}-${arch}.${ext}", "language": "1025"}, "dmg": {"title": "استمارة تقويم تقنية المعلومات", "icon": "assets/icon.icns", "background": "assets/dmg-background.png", "window": {"width": 540, "height": 380}, "contents": [{"x": 140, "y": 200, "type": "file"}, {"x": 400, "y": 200, "type": "link", "path": "/Applications"}]}, "publish": {"provider": "github", "owner": "your-github-username", "repo": "it-evaluation-system"}}, "keywords": ["education", "grading", "student-management", "arabic", "electron", "desktop-app", "school-management", "information-technology", "evaluation", "assessment"], "repository": {"type": "git", "url": "https://github.com/your-username/it-evaluation-system.git"}, "bugs": {"url": "https://github.com/your-username/it-evaluation-system/issues"}}